#!/usr/bin/env python3
"""
BinomoDataProvider - Template para implementação
Provedor de dados robusto para API Binomo substituindo MT5
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import time
import logging
from typing import Optional, Dict, List
import json
from threading import Lock
import hashlib

class BinomoDataProvider:
    """
    Provedor de dados para API Binomo com cache, rate limiting e retry logic
    """
    
    def __init__(self, config: dict):
        """
        Inicializa o provedor de dados
        
        Args:
            config: Dicionário com configurações da API
        """
        self.config = config
        self.base_url = config.get('BINOMO_API_BASE', 'https://api.binomo.com')
        self.symbol = config.get('SYMBOL', 'Z-CRY%2FIDX')
        self.locale = config.get('LOCALE', 'br')
        self.timeout = config.get('REQUEST_TIMEOUT', 10)
        self.max_retries = config.get('MAX_RETRIES', 3)
        self.cache_duration = config.get('CACHE_DURATION', 60)
        self.rate_limit = config.get('RATE_LIMIT_PER_MINUTE', 10)
        
        # Sistema de cache
        self.cache = {}
        self.cache_lock = Lock()
        
        # Rate limiting
        self.request_times = []
        self.rate_lock = Lock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Validação inicial
        self._validate_config()
        
    def _validate_config(self):
        """Valida configurações obrigatórias"""
        required_fields = ['BINOMO_API_BASE', 'SYMBOL']
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"Campo obrigatório ausente: {field}")
    
    def _wait_for_rate_limit(self):
        """Implementa rate limiting"""
        with self.rate_lock:
            now = time.time()
            # Remove requests antigos (mais de 1 minuto)
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Verifica se excedeu o limite
            if len(self.request_times) >= self.rate_limit:
                sleep_time = 60 - (now - self.request_times[0])
                if sleep_time > 0:
                    self.logger.warning(f"Rate limit atingido. Aguardando {sleep_time:.1f}s")
                    time.sleep(sleep_time)
            
            # Registra nova request
            self.request_times.append(now)
    
    def _generate_cache_key(self, timeframe: str, count: int, end_time: Optional[datetime] = None) -> str:
        """Gera chave única para cache"""
        end_str = end_time.isoformat() if end_time else "latest"
        key_data = f"{self.symbol}_{timeframe}_{count}_{end_str}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_data(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Recupera dados do cache se válidos"""
        with self.cache_lock:
            if cache_key in self.cache:
                data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < self.cache_duration:
                    self.logger.debug(f"Cache hit para {cache_key}")
                    return data.copy()
                else:
                    # Remove cache expirado
                    del self.cache[cache_key]
        return None
    
    def _cache_data(self, cache_key: str, data: pd.DataFrame):
        """Armazena dados no cache"""
        with self.cache_lock:
            self.cache[cache_key] = (data.copy(), time.time())
            
            # Limita tamanho do cache
            if len(self.cache) > 1000:
                # Remove 10% dos itens mais antigos
                items = list(self.cache.items())
                items.sort(key=lambda x: x[1][1])  # Ordena por timestamp
                for i in range(len(items) // 10):
                    del self.cache[items[i][0]]
    
    def _build_url(self, timeframe: str, timestamp: datetime) -> str:
        """Constrói URL da API"""
        timestamp_str = timestamp.strftime('%Y-%m-%dT%H:%M:%S')
        url = f"{self.base_url}/candles/v1/{self.symbol}/{timestamp_str}/{timeframe}"
        if self.locale:
            url += f"?locale={self.locale}"
        return url
    
    def _make_request(self, url: str) -> Optional[dict]:
        """Faz request HTTP com retry logic"""
        self._wait_for_rate_limit()
        
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"Request para: {url} (tentativa {attempt + 1})")
                
                response = requests.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success', True) and 'data' in data:
                        return data['data']
                    else:
                        self.logger.error(f"API retornou erro: {data.get('errors', 'Unknown error')}")
                        return None
                
                elif response.status_code == 429:  # Rate limited
                    wait_time = 2 ** attempt
                    self.logger.warning(f"Rate limited. Aguardando {wait_time}s")
                    time.sleep(wait_time)
                    continue
                
                else:
                    self.logger.error(f"HTTP {response.status_code}: {response.text[:200]}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Timeout na tentativa {attempt + 1}")
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Erro de request: {e}")
            
            # Backoff exponencial
            if attempt < self.max_retries:
                wait_time = (2 ** attempt) * self.config.get('RETRY_BACKOFF_FACTOR', 1.0)
                time.sleep(wait_time)
        
        return None
    
    def _convert_to_mt5_format(self, raw_data: List[dict]) -> pd.DataFrame:
        """Converte dados da API para formato compatível com MT5"""
        if not raw_data:
            return pd.DataFrame()
        
        # Converte para DataFrame
        df = pd.DataFrame(raw_data)
        
        # Mapeia campos da API Binomo para formato MT5
        column_mapping = {
            'created_at': 'time',
            'open': 'open',
            'high': 'high', 
            'low': 'low',
            'close': 'close'
        }
        
        # Renomeia colunas
        df = df.rename(columns=column_mapping)
        
        # Converte timestamp
        if 'time' in df.columns:
            df['time'] = pd.to_datetime(df['time'])
        
        # Adiciona colunas faltantes para compatibilidade MT5
        if 'tick_volume' not in df.columns:
            df['tick_volume'] = 1000  # Volume padrão
        if 'spread' not in df.columns:
            df['spread'] = 0
        if 'real_volume' not in df.columns:
            df['real_volume'] = df.get('tick_volume', 1000)
        
        # Ordena por tempo
        if 'time' in df.columns:
            df = df.sort_values('time').reset_index(drop=True)
        
        # Validação de dados
        if self.config.get('DATA_VALIDATION', True):
            df = self._validate_data(df)
        
        return df
    
    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Valida e limpa dados"""
        if df.empty:
            return df
        
        # Remove linhas com valores inválidos
        numeric_columns = ['open', 'high', 'low', 'close']
        for col in numeric_columns:
            if col in df.columns:
                df = df[df[col] > 0]  # Remove preços <= 0
                df = df[pd.notna(df[col])]  # Remove NaN
        
        # Valida OHLC
        if all(col in df.columns for col in numeric_columns):
            # High deve ser >= Open, Low, Close
            df = df[df['high'] >= df[['open', 'low', 'close']].max(axis=1)]
            # Low deve ser <= Open, High, Close  
            df = df[df['low'] <= df[['open', 'high', 'close']].min(axis=1)]
        
        return df.reset_index(drop=True)
    
    def get_historical_data(self, timeframe: str = "1", count: int = 20, 
                          end_time: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Obtém dados históricos da API Binomo
        
        Args:
            timeframe: Timeframe em minutos ("1", "5", etc.)
            count: Número de candles desejados
            end_time: Timestamp final (None para dados mais recentes)
            
        Returns:
            DataFrame com dados OHLC ou None em caso de erro
        """
        try:
            # Verifica cache primeiro
            cache_key = self._generate_cache_key(timeframe, count, end_time)
            cached_data = self._get_cached_data(cache_key)
            if cached_data is not None:
                return cached_data
            
            # Define timestamp para busca
            if end_time is None:
                end_time = datetime.now(timezone.utc)
            
            # Faz request para API
            url = self._build_url(timeframe, end_time)
            raw_data = self._make_request(url)
            
            if raw_data is None:
                self.logger.error("Falha ao obter dados da API")
                return None
            
            # Converte para formato MT5
            df = self._convert_to_mt5_format(raw_data)
            
            if df.empty:
                self.logger.warning("Nenhum dado válido retornado")
                return None
            
            # Limita ao número solicitado
            if len(df) > count:
                df = df.tail(count).reset_index(drop=True)
            
            # Armazena no cache
            self._cache_data(cache_key, df)
            
            self.logger.info(f"Obtidos {len(df)} candles para {timeframe}m")
            return df
            
        except Exception as e:
            self.logger.error(f"Erro ao obter dados históricos: {e}")
            return None
    
    def get_current_price(self) -> Optional[float]:
        """Obtém preço atual do ativo"""
        try:
            # Busca último candle
            df = self.get_historical_data(timeframe="1", count=1)
            if df is not None and len(df) > 0:
                return float(df.iloc[-1]['close'])
            return None
        except Exception as e:
            self.logger.error(f"Erro ao obter preço atual: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Testa conectividade com a API"""
        try:
            df = self.get_historical_data(timeframe="1", count=1)
            return df is not None and len(df) > 0
        except Exception as e:
            self.logger.error(f"Teste de conexão falhou: {e}")
            return False
    
    def get_available_symbols(self) -> List[str]:
        """Retorna símbolos disponíveis (implementação básica)"""
        return [self.symbol]  # Por enquanto apenas o símbolo configurado
    
    def shutdown(self):
        """Limpa recursos"""
        with self.cache_lock:
            self.cache.clear()
        self.logger.info("BinomoDataProvider finalizado")

# Exemplo de uso
if __name__ == "__main__":
    # Configuração de exemplo
    config = {
        'BINOMO_API_BASE': 'https://api.binomo.com',
        'SYMBOL': 'Z-CRY%2FIDX',
        'TIMEFRAME': '1',
        'LOCALE': 'br',
        'REQUEST_TIMEOUT': 10,
        'MAX_RETRIES': 3,
        'CACHE_DURATION': 60
    }
    
    # Teste básico
    provider = BinomoDataProvider(config)
    
    if provider.test_connection():
        print("✅ Conexão OK")
        df = provider.get_historical_data(count=5)
        if df is not None:
            print(f"✅ Obtidos {len(df)} candles")
            print(df.tail())
        else:
            print("❌ Falha ao obter dados")
    else:
        print("❌ Falha na conexão")
    
    provider.shutdown()
