import telebot
from telebot import types
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from typing import Optional
import threading

# Configuração de logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    direction: str  # 'COMPRA' ou 'VENDA'
    confidence: float
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    result: Optional[str] = None  # 'GAIN' ou 'LOSS'
    expiration_time: Optional[datetime] = None

class TradingBot:
    def __init__(self):
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False  # Flag para evitar múltiplos sinais simultâneos

    def _load_config(self):
        """Carrega variáveis do .env"""
        load_dotenv()
        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'symbol': os.getenv('TRADING_SYMBOL', 'CRYIDZbnm'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
        }

    def prepare_features(self, df):
        """Prepara os indicadores para sinais de compra e venda"""
        try:
            if len(df) < 20:
                logging.warning("Dados insuficientes para calcular indicadores (mínimo 20 períodos)")
                return None

            # Médias Móveis e Tendência
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
            df['trend'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            
            # Cálculo da força da tendência usando preço médio e amplitude
            avg_price = (df['high'] + df['low'] + df['close']) / 3
            df['price_range'] = df['high'] - df['low']
            df['trend_momentum'] = df['sma_5'].diff()
            df['trend_strength'] = (abs(df['trend_momentum']) / df['price_range'].rolling(window=5).mean() * 100).fillna(0)

            # Análise de Volume Avançada
            df['volume_ma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['tick_volume'] / df['volume_ma']
            df['volume_delta'] = df['tick_volume'].diff()
            df['volume_trend'] = df['volume_delta'].rolling(window=5, min_periods=1).apply(
                lambda x: np.sum(np.where(x > 0, 1, -1))
            )

            # Indicadores de Agressão
            df['price_range'] = np.maximum(df['high'] - df['low'], 1e-6)  # Evita divisão por zero
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_wick'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_wick'] = np.minimum(df['open'], df['close']) - df['low']
            
            # Cálculo de Agressão Avançado
            df['aggression_score'] = df['body_size'] * df['volume_ratio'] / df['price_range']
            df['buying_pressure'] = ((df['close'] - df['low']) / df['price_range']) * df['volume_ratio']
            df['selling_pressure'] = ((df['high'] - df['close']) / df['price_range']) * df['volume_ratio']
            
            # Momentum e Volatilidade
            df['roc'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5) * 100
            df['momentum'] = df['close'] - df['close'].shift(5)
            df['volatility'] = df['close'].rolling(window=5, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()

            # Preenche valores NaN
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
            
            return df
        except Exception as e:
            logging.error(f"Erro ao preparar features: {str(e)}")
            return None

    def predict_next_move(self):
        """Faz a previsão do próximo movimento com análise avançada"""
        try:
            rates = mt5.copy_rates_from_pos(self.config['symbol'], mt5.TIMEFRAME_M1, 0, 20)
            if rates is None or len(rates) == 0:
                logging.error("Erro ao obter dados para previsão.")
                return None, 0.0

            df = pd.DataFrame(rates)
            features = self.prepare_features(df)
            if features is None:
                logging.warning("Falha ao preparar features para análise.")
                return None, 0.0
            
            # Sistema de pontuação para sinais
            last_row = features.iloc[-1]
            score = 50.0  # Valor inicial neutro
            signals = []

            try:
                # Análise de Tendência (30% do peso)
                if last_row['trend'] == 1:
                    trend_score = min(last_row['trend_strength'], 30)
                    score += trend_score
                    signals.append(('Tendência de alta', trend_score))
                else:
                    trend_score = min(last_row['trend_strength'], 30)
                    score -= trend_score
                    signals.append(('Tendência de baixa', trend_score))
                
                # Análise de Volume (25% do peso)
                volume_score = 0
                if last_row['volume_ratio'] > 1.5:
                    volume_score += 15
                    signals.append(('Volume forte', last_row['volume_ratio']))
                if last_row['volume_trend'] > 0:
                    volume_score += 10
                    signals.append(('Tendência volume positiva', last_row['volume_trend']))
                score += volume_score

                # Análise de Pressão (25% do peso)
                pressure_score = (last_row['buying_pressure'] - last_row['selling_pressure']) * 25
                score += pressure_score
                signals.append(('Pressão', pressure_score))

                # Análise de Agressão (20% do peso)
                if last_row['aggression_score'] > 0.6:
                    score += 20
                    signals.append(('Alta agressão', last_row['aggression_score']))

                # Normaliza score entre 0 e 100
                score = max(0, min(100, score))

                # Define a direção com base na confiança
                direction = '🟩COMPRA' if score > 55 else '🟥VENDA' if score < 45 else None
                if direction is None:
                    logging.info("Sinal neutro - confiança insuficiente")
                    return None, 0.0

                confidence = score if direction == 'COMPRA' else (100 - score)
                logging.info(f"Score final: {score:.2f}, Sinais: {signals}")
                return direction, confidence
            except Exception as e:
                logging.error(f"Erro ao calcular sinais: {str(e)}")
                return None, 0.0

        except Exception as e:
            logging.error(f"Erro ao prever movimento: {str(e)}")
            return None, 0.0

    def wait_for_next_minute(self):
        """Aguarda o início do próximo minuto."""
        now = datetime.now()
        next_minute = (now + timedelta(minutes=1)).replace(second=0, microsecond=0)
        sleep_time = (next_minute - now).total_seconds()
        logging.info(f"Aguardando {sleep_time:.1f} segundos até o próximo minuto...")
        time.sleep(sleep_time)

    def get_current_price(self):
        """Obtém o preço atual do ativo."""
        tick = mt5.symbol_info_tick(self.config['symbol'])
        if tick:
            price_candidates = [tick.last, tick.ask, tick.bid]
            price = next((p for p in price_candidates if p and p > 0), None)
            return price
        return None

    def gerar_sinal(self):
        """Gera sinal, aguarda o fechamento do minuto para dar os sinais de compra ou venda com expiração."""
        # Evita processamento concorrente de sinais
        if self.processing_signal:
            return "⚠️ Aguarde o fim do sinal atual."

        self.processing_signal = True
        try:
            # 1. Gerar Sinal
            operation, probability = self.predict_next_move()
            if operation is None:
                self.processing_signal = False
                return "⚠️ Nenhum sinal gerado."

            # Aguarda o fechamento do minuto antes de obter o preço de entrada
            self.wait_for_next_minute()

            # Obter preço de entrada após o fechamento do minuto
            entry_price = self.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(minutes=3)  # M3
            self.active_trade = TradeResult(entry_time, entry_price, operation, probability, expiration_time=expiration_time)

            try:
                # Análise técnica
                rates = mt5.copy_rates_from_pos(self.config['symbol'], mt5.TIMEFRAME_M1, 0, 20)
                if rates is None or len(rates) == 0:
                    self.processing_signal = False
                    return "⚠️ Erro ao obter dados para análise técnica."

                df = pd.DataFrame(rates)
                features = self.prepare_features(df)
                if features is None:
                    self.processing_signal = False
                    return "⚠️ Erro ao analisar dados técnicos."

                last_row = features.iloc[-1]

                # 2. Enviar Sinal
                message = (
                    f"🎯 **SINAL DE {operation}**\n"
                    f"📈 Ativo: {self.config['symbol']}\n"
                    f"⏳ Expiração: {expiration_time.strftime('%H:%M:%S')} (M3)\n\n"
                    f"📊 Confiança: {probability:.2f}%\n"
                   
                    f"📈 **Análise Técnica**\n"
                    f"Volume: {'🟢' if last_row['volume_ratio'] > 1 else '🔴'} ({last_row['volume_ratio']:.2f}x média)\n"
                    f"Agressão: {'🟢' if last_row['aggression_score'] > 0.5 else '🔴'} ({last_row['aggression_score']:.2f})\n"
                    f"Tendência: {'🟢' if last_row['trend'] > 0 else '🔴'} (Força: {last_row['trend_strength']:.1f}%)\n"
                    f"Volatilidade: {'🟢' if last_row['volatility_ratio'] > 1 else '🔴'} ({last_row['volatility_ratio']:.2f}x média)"
                )

                self.bot.send_message(self.config['chat_id'], message)
                logging.info(f"Sinal enviado: {message}")

                # 4. Calcular Resultado
                exit_price = self.get_current_price()
                if exit_price is None:
                    self.processing_signal = False
                    return "⚠️ Erro ao obter preço de fechamento."

                if operation == 'COMPRA':
                    result = "✅GAIN 📈" if exit_price > entry_price else "❌LOSS 📉"
                else:  # VENDA
                    result = "✅GAIN 📈" if exit_price < entry_price else "❌LOSS 📉"

                price_change = abs(exit_price - entry_price) / entry_price * 100

                result_message = (
                    f"🔄 **RESULTADO DO TRADE**\n"
                    f"Resultado: {result}"
                )

                self.bot.send_message(self.config['chat_id'], result_message)
                logging.info(f"Resultado enviado: {result_message}")
                self.processing_signal = False
                return message

            except Exception as e:
                logging.error(f"Erro ao processar trade: {str(e)}")
                self.processing_signal = False
                return "⚠️ Erro ao processar trade."

        except Exception as e:
            logging.error(f"Erro ao gerar sinal: {str(e)}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."

    def _setup_telegram_handlers(self):
        """Configura comandos no Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            self.bot.send_message(message.chat.id, "🤖 Bot de Trading Ativo! Use /sinal para obter um sinal.")

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal()
            if isinstance(sinal, str):
                self.bot.send_message(message.chat.id, sinal)

    def run(self):
        """Executa o bot"""
        if not mt5.initialize():
            logging.error("Erro ao inicializar MT5")
            return
        logging.info("🚀 Bot de trading iniciado!")
        self.bot.polling()

if __name__ == "__main__":
    bot = TradingBot()
    bot.run()
