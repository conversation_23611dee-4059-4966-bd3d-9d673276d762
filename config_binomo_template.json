{"_comment": "Configuração para API Binomo - Template para implementação", "API_SETTINGS": {"BINOMO_API_BASE": "https://api.binomo.com", "SYMBOL": "Z-CRY%2FIDX", "LOCALE": "br", "REQUEST_TIMEOUT": 10, "MAX_RETRIES": 3, "RETRY_BACKOFF_FACTOR": 2.0}, "TRADING_SETTINGS": {"TIMEFRAME": "1", "DEFAULT_CANDLE_COUNT": 20, "MIN_CONFIDENCE": 55.0, "EXPIRATION_MINUTES": 3}, "CACHE_SETTINGS": {"ENABLE_CACHE": true, "CACHE_DURATION": 60, "MAX_CACHE_ENTRIES": 1000, "CACHE_CLEANUP_INTERVAL": 300}, "RATE_LIMITING": {"RATE_LIMIT_PER_MINUTE": 10, "BURST_LIMIT": 5, "COOLDOWN_PERIOD": 60}, "DATA_VALIDATION": {"ENABLE_VALIDATION": true, "MIN_PRICE": 1e-05, "MAX_PRICE": 999999.99, "VALIDATE_OHLC": true, "REMOVE_OUTLIERS": true}, "LOGGING": {"LOG_LEVEL": "INFO", "LOG_API_CALLS": true, "LOG_CACHE_HITS": false, "LOG_PERFORMANCE": true, "MAX_LOG_SIZE_MB": 50}, "MONITORING": {"ENABLE_METRICS": true, "METRICS_INTERVAL": 60, "ALERT_ON_ERRORS": true, "PERFORMANCE_THRESHOLD_MS": 2000}, "FALLBACK": {"ENABLE_FALLBACK": false, "FALLBACK_PROVIDER": "MT5", "FALLBACK_TIMEOUT": 30, "AUTO_RECOVERY": true}, "ADVANCED": {"ASYNC_REQUESTS": false, "CONNECTION_POOL_SIZE": 10, "KEEP_ALIVE": true, "COMPRESSION": true, "USER_AGENT": "BinomoBot/1.0"}}