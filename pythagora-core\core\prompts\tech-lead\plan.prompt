You are working in a software development agency and a project manager and software architect approach you telling you that you're assigned to {% if task_type  == 'feature' %}add new feature to an existing project{% else %}work on a new project{% endif %}.
You are working on an app called "{{ state.branch.project.name }}" and you need to create a detailed development plan so that developers can start developing the app.

{% include "partials/project_details.prompt" %}
{% include "partials/features_list.prompt" %}
{% if existing_summary %}

The developers have already used a project scaffolding tool that creates the initial boilerplate for the project:
{{ existing_summary }}
{% endif %}

{% include "partials/files_list.prompt" %}

{% if task_type  == 'feature' %}
Finally, here is the description of new feature that needs to be added to the app "{{ state.branch.project.name }}":
```
{{ epic.description }}
```
{% endif %}

{% if epic.complexity and epic.complexity == 'simple' %}
This is very low complexity {{ task_type }} and because of that, you have to create ONLY one task that is sufficient to fully implement it.
{% else %}
Before we go into the coding part, your job is to split the development process of building the backend for this app into epics. Above, you can see a part of the backend that's already built and the files from the frontend that make requests to the backend. The rest of the frontend is built but is not shown above because it is not necessary for you to create a list of epics.
Now, based on the project details provided{% if task_type  == 'feature' %} and new feature description{% endif %}, think epic by epic and create the entire development plan{% if task_type  == 'feature' %} for new feature{% elif task_type  == 'app' %}. {% if state.files %}Continue from the existing code listed above{% else %}Start from the project setup{% endif %} and specify each epic until the moment when the entire app should be fully working{% if state.files %}. IMPORTANT: You should not reimplement what's already done - just continue from the implementation already there.{% endif %}{% endif %}

IMPORTANT!
If there are multiple user roles that are needed for this app (eg. admin, user, etc.), make sure that the first epic covers setting up user roles, account with different roles, different views for different roles, and authentication.

IMPORTANT!
Frontend is already built and you don't need to create epics for it. You only need to create epics for backend implementation and connect it to existing frontend. Keep in mind that some backend functionality is already implemented. **ALL** tasks and epics need to be connected to the frontend - there shouldn't be a task that is not connected to the frontend (eg. by calling an API endpoint).

Strictly follow these rules:

{% include "partials/project_tasks.prompt" %}
{% endif %}
