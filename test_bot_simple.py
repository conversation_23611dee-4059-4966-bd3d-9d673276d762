#!/usr/bin/env python3
"""Teste simples do bot"""

print("Iniciando teste do bot...")

try:
    print("1. Importando módulos...")
    from binomo_data_provider import BinomoDataProvider
    import json
    print("   ✅ Imports OK")
    
    print("2. Carregando configuração...")
    with open('config_binomo.json', 'r') as f:
        config = json.load(f)
    print("   ✅ Config OK")
    
    print("3. Inicializando BinomoDataProvider...")
    provider = BinomoDataProvider(config)
    print("   ✅ Provider OK")
    
    print("4. Testando conexão...")
    if provider.test_connection():
        print("   ✅ Conexão OK")
    else:
        print("   ❌ Conexão falhou")
    
    print("5. Testando obtenção de dados...")
    df = provider.get_historical_data(timeframe="1", count=5)
    if df is not None and len(df) > 0:
        print(f"   ✅ Dados OK - {len(df)} candles")
    else:
        print("   ❌ Dados falharam")
    
    print("6. Testando preço atual...")
    price = provider.get_current_price()
    if price is not None:
        print(f"   ✅ Preço OK - {price}")
    else:
        print("   ❌ Preço falhou")
    
    print("\n🎉 TODOS OS TESTES PASSARAM!")
    
except Exception as e:
    print(f"❌ ERRO: {e}")
    import traceback
    traceback.print_exc()
