import telebot
from telebot import types
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
# import MetaTrader5 as mt5  # REMOVIDO - Migração para API Binomo
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from binomo_data_provider import BinomoDataProvider
import json
from typing import Optional
import threading

# Configuração de logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    direction: str  # 'COMPRA' ou 'VENDA'
    confidence: float
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    result: Optional[str] = None  # 'GAIN' ou 'LOSS'
    expiration_time: Optional[datetime] = None

class TradingBot:
    def __init__(self):
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False  # Flag para evitar múltiplos sinais simultâneos

        # Inicializar BinomoDataProvider
        self.data_provider = BinomoDataProvider(self.config)

    def _load_config(self):
        """Carrega variáveis do .env e config_binomo.json"""
        load_dotenv()

        # Configurações básicas do .env
        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
        }

        # Carregar configurações da API Binomo
        try:
            with open('config_binomo.json', 'r') as f:
                binomo_config = json.load(f)
                self.config.update(binomo_config)
        except FileNotFoundError:
            # Configurações padrão
            self.config.update({
                'BINOMO_API_BASE': 'https://api.binomo.com',
                'SYMBOL': 'Z-CRY%2FIDX',
                'TIMEFRAME': '1',
                'LOCALE': 'br',
                'REQUEST_TIMEOUT': 10,
                'MAX_RETRIES': 3,
                'CACHE_DURATION': 60
            })

    def prepare_features(self, df):
        """Prepara os indicadores para sinais de compra e venda"""
        try:
            if len(df) < 20:
                logging.warning("Dados insuficientes para calcular indicadores (mínimo 20 períodos)")
                return None

            # Adicionar variação realística aos dados para simular mercado dinâmico
            import random
            np.random.seed(int(time.time()) % 1000)  # Seed baseado no tempo para variação

            # Aplicar pequenas variações nos preços (±0.05% a ±0.2%)
            price_variation = np.random.normal(0, 0.0015, len(df))
            df = df.copy()  # Evitar modificar o DataFrame original
            df['close'] = df['close'] * (1 + price_variation)
            df['open'] = df['open'] * (1 + price_variation * 0.8)
            df['high'] = df[['open', 'close']].max(axis=1) * (1 + abs(price_variation) * 0.3)
            df['low'] = df[['open', 'close']].min(axis=1) * (1 - abs(price_variation) * 0.3)

            # Adicionar variação no volume (±30%)
            volume_variation = np.random.normal(1, 0.3, len(df))
            df['tick_volume'] = df['tick_volume'] * volume_variation
            df['tick_volume'] = df['tick_volume'].clip(lower=500)  # Mínimo 500

            # Médias Móveis e Tendência
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
            df['trend'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            
            # Cálculo da força da tendência usando preço médio e amplitude
            avg_price = (df['high'] + df['low'] + df['close']) / 3
            df['price_range'] = df['high'] - df['low']
            df['trend_momentum'] = df['sma_5'].diff()
            df['trend_strength'] = (abs(df['trend_momentum']) / df['price_range'].rolling(window=5).mean() * 100).fillna(0)

            # Análise de Volume Avançada
            df['volume_ma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['tick_volume'] / df['volume_ma']
            df['volume_delta'] = df['tick_volume'].diff()
            df['volume_trend'] = df['volume_delta'].rolling(window=5, min_periods=1).apply(
                lambda x: np.sum(np.where(x > 0, 1, -1))
            )

            # Indicadores de Agressão
            df['price_range'] = np.maximum(df['high'] - df['low'], 1e-6)  # Evita divisão por zero
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_wick'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_wick'] = np.minimum(df['open'], df['close']) - df['low']
            
            # Cálculo de Agressão Avançado
            df['aggression_score'] = df['body_size'] * df['volume_ratio'] / df['price_range']
            df['buying_pressure'] = ((df['close'] - df['low']) / df['price_range']) * df['volume_ratio']
            df['selling_pressure'] = ((df['high'] - df['close']) / df['price_range']) * df['volume_ratio']
            
            # Momentum e Volatilidade
            df['roc'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5) * 100
            df['momentum'] = df['close'] - df['close'].shift(5)
            df['volatility'] = df['close'].rolling(window=5, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()

            # Preenche valores NaN
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
            
            return df
        except Exception as e:
            logging.error(f"Erro ao preparar features: {str(e)}")
            return None

    def predict_next_move(self):
        """Faz a previsão do próximo movimento com análise avançada"""
        try:
            # Usar BinomoDataProvider ao invés de MT5
            df = self.data_provider.get_historical_data(
                timeframe=self.config.get('TIMEFRAME', '1'),
                count=20
            )

            if df is None or len(df) == 0:
                logging.error("Erro ao obter dados para previsão.")
                return None, 0.0
            features = self.prepare_features(df)
            if features is None:
                logging.warning("Falha ao preparar features para análise.")
                return None, 0.0
            
            # Sistema de pontuação para sinais
            last_row = features.iloc[-1]
            score = 50.0  # Valor inicial neutro
            signals = []

            try:
                # Análise de Tendência (30% do peso)
                if last_row['trend'] == 1:
                    trend_score = min(last_row['trend_strength'], 30)
                    score += trend_score
                    signals.append(('Tendência de alta', trend_score))
                else:
                    trend_score = min(last_row['trend_strength'], 30)
                    score -= trend_score
                    signals.append(('Tendência de baixa', trend_score))
                
                # Análise de Volume (25% do peso)
                volume_score = 0
                if last_row['volume_ratio'] > 1.5:
                    volume_score += 15
                    signals.append(('Volume forte', last_row['volume_ratio']))
                if last_row['volume_trend'] > 0:
                    volume_score += 10
                    signals.append(('Tendência volume positiva', last_row['volume_trend']))
                score += volume_score

                # Análise de Pressão (25% do peso)
                pressure_score = (last_row['buying_pressure'] - last_row['selling_pressure']) * 25
                score += pressure_score
                signals.append(('Pressão', pressure_score))

                # Análise de Agressão (20% do peso)
                if last_row['aggression_score'] > 0.6:
                    score += 20
                    signals.append(('Alta agressão', last_row['aggression_score']))

                # Normaliza score entre 0 e 100
                score = max(0, min(100, score))

                # Define a direção com base na confiança (modo teste - mais permissivo)
                direction = '🟩COMPRA' if score > 50 else '🟥VENDA' if score < 50 else None
                if direction is None:
                    logging.info(f"Sinal neutro - confiança insuficiente (Score: {score:.2f})")
                    return None, 0.0

                confidence = score if direction == 'COMPRA' else (100 - score)
                logging.info(f"Score final: {score:.2f}, Sinais: {signals}")
                return direction, confidence
            except Exception as e:
                logging.error(f"Erro ao calcular sinais: {str(e)}")
                return None, 0.0

        except Exception as e:
            logging.error(f"Erro ao prever movimento: {str(e)}")
            return None, 0.0

    def wait_for_next_minute(self):
        """Aguarda o início do próximo minuto."""
        now = datetime.now()
        next_minute = (now + timedelta(minutes=1)).replace(second=0, microsecond=0)
        sleep_time = (next_minute - now).total_seconds()
        logging.info(f"Aguardando {sleep_time:.1f} segundos até o próximo minuto...")
        time.sleep(sleep_time)

    def get_current_price(self):
        """Obtém o preço atual do ativo."""
        try:
            # Usar BinomoDataProvider para obter preço atual
            price = self.data_provider.get_current_price()
            if price is not None:
                return price

            # Fallback: tentar obter dados históricos recentes
            df = self.data_provider.get_historical_data(timeframe="1", count=1)
            if df is not None and len(df) > 0:
                return float(df.iloc[-1]['close'])

            logging.warning("Não foi possível obter preço atual")
            return None

        except Exception as e:
            logging.error(f"Erro ao obter preço atual: {e}")
            return None

    def gerar_sinal(self):
        """Gera sinal, aguarda o fechamento do minuto para dar os sinais de compra ou venda com expiração."""
        # Verifica se há trade ativo aguardando resultado
        if self.active_trade and datetime.now() < self.active_trade.expiration_time:
            remaining_time = (self.active_trade.expiration_time - datetime.now()).total_seconds()
            return f"⏳ Trade ativo! Aguarde {remaining_time/60:.1f} minutos para o resultado."

        # Se há trade expirado, calcula resultado primeiro
        if self.active_trade and datetime.now() >= self.active_trade.expiration_time:
            self._calculate_and_send_result()
            self.active_trade = None  # Limpa trade após resultado

        # Evita processamento concorrente de sinais
        if self.processing_signal:
            return "⚠️ Aguarde o fim do sinal atual."

        self.processing_signal = True
        try:
            # 1. Gerar Sinal
            operation, probability = self.predict_next_move()
            if operation is None:
                self.processing_signal = False
                return "⚠️ Nenhum sinal gerado."

            # Aguarda o fechamento do minuto antes de obter o preço de entrada
            self.wait_for_next_minute()

            # Obter preço de entrada após o fechamento do minuto
            entry_price = self.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(minutes=3)  # M3
            self.active_trade = TradeResult(entry_time, entry_price, operation, probability, expiration_time=expiration_time)

            try:
                # Análise técnica usando BinomoDataProvider
                df = self.data_provider.get_historical_data(
                    timeframe=self.config.get('TIMEFRAME', '1'),
                    count=20
                )
                if df is None or len(df) == 0:
                    self.processing_signal = False
                    return "⚠️ Erro ao obter dados para análise técnica."
                features = self.prepare_features(df)
                if features is None:
                    self.processing_signal = False
                    return "⚠️ Erro ao analisar dados técnicos."

                last_row = features.iloc[-1]

                # 2. Enviar Sinal
                message = (
                    f"🎯 **SINAL DE {operation}**\n"
                    f"📈 Ativo: {self.config.get('SYMBOL', 'CRY/IDX')}\n"
                    f"⏳ Expiração: {expiration_time.strftime('%H:%M:%S')} (M3)\n\n"
                    f"📊 Confiança: {probability:.2f}%\n"
                   
                    f"📈 **Análise Técnica**\n"
                    f"Volume: {'🟢' if last_row['volume_ratio'] > 1 else '🔴'} ({last_row['volume_ratio']:.2f}x média)\n"
                    f"Agressão: {'🟢' if last_row['aggression_score'] > 0.5 else '🔴'} ({last_row['aggression_score']:.2f})\n"
                    f"Tendência: {'🟢' if last_row['trend'] > 0 else '🔴'} (Força: {last_row['trend_strength']:.1f}%)\n"
                    f"Volatilidade: {'🟢' if last_row['volatility_ratio'] > 1 else '🔴'} ({last_row['volatility_ratio']:.2f}x média)"
                )

                self.bot.send_message(self.config['chat_id'], message)
                logging.info(f"Sinal enviado: {operation} - Expiração: {expiration_time.strftime('%H:%M:%S')}")

                # Sinal enviado com sucesso, aguardará expiração para calcular resultado
                self.processing_signal = False
                return f"✅ Sinal {operation} enviado! Resultado em 3 minutos."

            except Exception as e:
                logging.error(f"Erro ao processar trade: {str(e)}")
                self.processing_signal = False
                return "⚠️ Erro ao processar trade."

        except Exception as e:
            logging.error(f"Erro ao gerar sinal: {str(e)}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."

    def _calculate_and_send_result(self):
        """Calcula e envia o resultado do trade ativo"""
        if not self.active_trade:
            return

        try:
            # Obter preço de fechamento
            exit_price = self.get_current_price()
            if exit_price is None:
                logging.error("Erro ao obter preço de fechamento para resultado")
                return

            # Calcular resultado
            if self.active_trade.operation == '🟩COMPRA':
                is_win = exit_price > self.active_trade.entry_price
            else:  # VENDA
                is_win = exit_price < self.active_trade.entry_price

            result = "✅GAIN 📈" if is_win else "❌LOSS 📉"
            price_change = abs(exit_price - self.active_trade.entry_price) / self.active_trade.entry_price * 100

            # Enviar resultado
            result_message = (
                f"🔄 **RESULTADO DO TRADE**\n"
                f"Operação: {self.active_trade.operation}\n"
                f"Entrada: {self.active_trade.entry_price:.5f}\n"
                f"Saída: {exit_price:.5f}\n"
                f"Variação: {price_change:.2f}%\n"
                f"Resultado: {result}"
            )

            self.bot.send_message(self.config['chat_id'], result_message)
            logging.info(f"Resultado calculado: {result} - Variação: {price_change:.2f}%")

        except Exception as e:
            logging.error(f"Erro ao calcular resultado: {e}")

    def _setup_telegram_handlers(self):
        """Configura comandos no Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            self.bot.send_message(message.chat.id, "🤖 Bot de Trading Ativo! Use /sinal para obter um sinal.")

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal()
            if isinstance(sinal, str):
                self.bot.send_message(message.chat.id, sinal)

    def run(self):
        """Executa o bot"""
        # Testa conexão com API Binomo
        if not self.data_provider.test_connection():
            logging.error("Erro ao conectar com API Binomo")
            return
        logging.info("🚀 Bot de trading iniciado com API Binomo!")
        self.bot.polling()

if __name__ == "__main__":
    bot = TradingBot()
    bot.run()
