#!/usr/bin/env python3
"""
Sistema de Backtesting e Otimização de Thresholds
Testa estratégia com histórico maior e otimiza parâmetros automaticamente
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
from binomo_realtime_provider import BinomoRealtimeProvider
from bot_strategy_optimized import OptimizedTradingBot
import logging
from typing import Dict, List, Tuple
import itertools
from dataclasses import dataclass
import matplotlib.pyplot as plt

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class BacktestResult:
    """Resultado de um backtest"""
    total_trades: int
    wins: int
    losses: int
    win_rate: float
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    avg_trade_duration: float
    confidence_distribution: Dict[str, int]
    thresholds: Dict[str, float]

@dataclass
class Trade:
    """Representa um trade no backtesting"""
    entry_time: datetime
    entry_price: float
    exit_time: datetime
    exit_price: float
    direction: str
    confidence: str
    score: float
    result: str
    pnl: float

class BacktestingOptimizer:
    def __init__(self):
        """Inicializa o sistema de backtesting"""
        # Carregar configuração
        with open('config_binomo.json', 'r') as f:
            self.config = json.load(f)
        
        # Inicializar bot (sem Telegram)
        self.bot = OptimizedTradingBot()
        self.bot.config['chat_id'] = None
        
        # Histórico de dados para backtesting
        self.historical_data = []
        self.backtest_results = []
        
        print("🔬 Sistema de Backtesting e Otimização inicializado")

    def collect_historical_data(self, duration_hours: int = 24):
        """Coleta dados históricos para backtesting"""
        print(f"📊 Coletando dados históricos ({duration_hours}h)...")
        
        # Simular coleta de dados históricos (em produção, usar API real)
        data_points = duration_hours * 60  # 1 ponto por minuto
        
        # Gerar dados simulados mais realísticos
        np.random.seed(42)  # Para reprodutibilidade
        base_price = 641.867
        
        historical_data = []
        current_time = datetime.now() - timedelta(hours=duration_hours)
        
        for i in range(data_points):
            # Simular movimento de preço com tendências
            trend_factor = np.sin(i / 100) * 0.002  # Tendência cíclica
            noise = np.random.normal(0, 0.001)  # Ruído
            price_change = trend_factor + noise
            
            new_price = base_price * (1 + price_change)
            
            # Simular OHLC
            high_var = abs(np.random.normal(0, 0.0005))
            low_var = abs(np.random.normal(0, 0.0005))
            
            candle = {
                'time': current_time + timedelta(minutes=i),
                'open': base_price,
                'high': max(base_price, new_price) + high_var,
                'low': min(base_price, new_price) - low_var,
                'close': new_price,
                'tick_volume': int(np.random.normal(1000, 200)),
                'spread': 0,
                'real_volume': int(np.random.normal(1000, 200))
            }
            
            historical_data.append(candle)
            base_price = new_price
        
        self.historical_data = pd.DataFrame(historical_data)
        print(f"✅ Coletados {len(self.historical_data)} pontos de dados")
        return self.historical_data

    def run_backtest(self, thresholds: Dict[str, Dict[str, float]], 
                    start_idx: int = 30, end_idx: int = None) -> BacktestResult:
        """Executa backtest com thresholds específicos"""
        
        if end_idx is None:
            end_idx = len(self.historical_data)
        
        trades = []
        active_trade = None
        
        # Aplicar thresholds temporariamente
        original_method = self.bot.advanced_strategy_v2
        
        def modified_strategy(df):
            direction, score, signals, confidence = original_method(df)
            
            # Aplicar thresholds customizados
            if confidence in thresholds:
                threshold_buy = thresholds[confidence]['buy']
                threshold_sell = thresholds[confidence]['sell']
                
                if score > threshold_buy:
                    direction = '🟩COMPRA'
                elif score < threshold_sell:
                    direction = '🟥VENDA'
                else:
                    direction = None
            
            return direction, score, signals, confidence
        
        self.bot.advanced_strategy_v2 = modified_strategy
        
        # Executar backtest
        for i in range(start_idx, end_idx):
            current_time = self.historical_data.iloc[i]['time']
            current_price = self.historical_data.iloc[i]['close']
            
            # Verificar se trade ativo expirou
            if active_trade and current_time >= active_trade.exit_time:
                # Calcular resultado
                exit_price = current_price
                
                if active_trade.direction == '🟩COMPRA':
                    is_win = exit_price > active_trade.entry_price
                else:
                    is_win = exit_price < active_trade.entry_price
                
                pnl = ((exit_price - active_trade.entry_price) / active_trade.entry_price) * 100
                if active_trade.direction == '🟥VENDA':
                    pnl = -pnl
                
                trade = Trade(
                    entry_time=active_trade.entry_time,
                    entry_price=active_trade.entry_price,
                    exit_time=current_time,
                    exit_price=exit_price,
                    direction=active_trade.direction,
                    confidence=active_trade.confidence,
                    score=active_trade.score,
                    result="WIN" if is_win else "LOSS",
                    pnl=pnl
                )
                
                trades.append(trade)
                active_trade = None
            
            # Gerar novo sinal se não há trade ativo
            if active_trade is None:
                # Obter dados para análise (últimos 30 candles)
                df_slice = self.historical_data.iloc[max(0, i-29):i+1].copy()
                
                if len(df_slice) >= 20:
                    try:
                        direction, score, signals, confidence = self.bot.advanced_strategy_v2(df_slice)
                        
                        if direction:
                            # Criar trade ativo
                            active_trade = type('Trade', (), {
                                'entry_time': current_time,
                                'entry_price': current_price,
                                'exit_time': current_time + timedelta(minutes=3),
                                'direction': direction,
                                'confidence': confidence,
                                'score': score
                            })()
                    
                    except Exception as e:
                        continue
        
        # Restaurar método original
        self.bot.advanced_strategy_v2 = original_method
        
        # Calcular métricas
        if not trades:
            return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, {}, thresholds)
        
        total_trades = len(trades)
        wins = len([t for t in trades if t.result == "WIN"])
        losses = total_trades - wins
        win_rate = (wins / total_trades) * 100
        
        total_return = sum([t.pnl for t in trades])
        
        # Calcular drawdown
        cumulative_pnl = np.cumsum([t.pnl for t in trades])
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdown = running_max - cumulative_pnl
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0
        
        # Sharpe ratio simplificado
        returns = [t.pnl for t in trades]
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        # Distribuição de confiança
        confidence_dist = {}
        for trade in trades:
            conf = trade.confidence
            if conf not in confidence_dist:
                confidence_dist[conf] = 0
            confidence_dist[conf] += 1
        
        avg_duration = 3.0  # Fixo em 3 minutos
        
        return BacktestResult(
            total_trades=total_trades,
            wins=wins,
            losses=losses,
            win_rate=win_rate,
            total_return=total_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            avg_trade_duration=avg_duration,
            confidence_distribution=confidence_dist,
            thresholds=thresholds
        )

    def optimize_thresholds(self, optimization_rounds: int = 50) -> Dict[str, Dict[str, float]]:
        """Otimiza thresholds usando grid search inteligente"""
        print(f"🎯 Iniciando otimização de thresholds ({optimization_rounds} rounds)...")
        
        # Definir ranges de otimização
        confidence_levels = ['MUITO_ALTA', 'ALTA', 'MEDIA', 'BAIXA']
        
        # Ranges mais focados baseados nos resultados anteriores
        threshold_ranges = {
            'MUITO_ALTA': {'buy': [50, 51, 52], 'sell': [48, 49, 50]},
            'ALTA': {'buy': [51, 52, 53], 'sell': [47, 48, 49]},
            'MEDIA': {'buy': [52, 53, 54], 'sell': [46, 47, 48]},
            'BAIXA': {'buy': [54, 55, 56], 'sell': [44, 45, 46]}
        }
        
        best_result = None
        best_thresholds = None
        all_results = []
        
        # Gerar combinações de thresholds
        combinations = []
        for muito_alta_buy in threshold_ranges['MUITO_ALTA']['buy']:
            for muito_alta_sell in threshold_ranges['MUITO_ALTA']['sell']:
                for alta_buy in threshold_ranges['ALTA']['buy']:
                    for alta_sell in threshold_ranges['ALTA']['sell']:
                        for media_buy in threshold_ranges['MEDIA']['buy']:
                            for media_sell in threshold_ranges['MEDIA']['sell']:
                                for baixa_buy in threshold_ranges['BAIXA']['buy']:
                                    for baixa_sell in threshold_ranges['BAIXA']['sell']:
                                        
                                        # Validar lógica dos thresholds
                                        if (muito_alta_buy > muito_alta_sell and
                                            alta_buy > alta_sell and
                                            media_buy > media_sell and
                                            baixa_buy > baixa_sell):
                                            
                                            thresholds = {
                                                'MUITO_ALTA': {'buy': muito_alta_buy, 'sell': muito_alta_sell},
                                                'ALTA': {'buy': alta_buy, 'sell': alta_sell},
                                                'MEDIA': {'buy': media_buy, 'sell': media_sell},
                                                'BAIXA': {'buy': baixa_buy, 'sell': baixa_sell}
                                            }
                                            combinations.append(thresholds)
        
        # Limitar número de combinações se necessário
        if len(combinations) > optimization_rounds:
            # Selecionar amostra representativa
            step = len(combinations) // optimization_rounds
            combinations = combinations[::step][:optimization_rounds]
        
        print(f"🔍 Testando {len(combinations)} combinações de thresholds...")
        
        # Dividir dados em treino (70%) e teste (30%)
        train_size = int(len(self.historical_data) * 0.7)
        
        for i, thresholds in enumerate(combinations):
            try:
                # Backtest no conjunto de treino
                result = self.run_backtest(thresholds, start_idx=30, end_idx=train_size)
                
                # Função objetivo: maximizar win rate * total return - max drawdown
                if result.total_trades >= 10:  # Mínimo de trades para ser válido
                    objective_score = (result.win_rate * result.total_return / 100) - (result.max_drawdown * 0.5)
                    result.objective_score = objective_score
                    
                    all_results.append(result)
                    
                    if best_result is None or objective_score > best_result.objective_score:
                        best_result = result
                        best_thresholds = thresholds
                
                # Progress
                if (i + 1) % 10 == 0:
                    print(f"   Progresso: {i+1}/{len(combinations)} ({(i+1)/len(combinations)*100:.1f}%)")
                    
            except Exception as e:
                continue
        
        print(f"✅ Otimização concluída!")
        
        if best_result:
            print(f"🏆 Melhor configuração encontrada:")
            print(f"   Win Rate: {best_result.win_rate:.1f}%")
            print(f"   Total Return: {best_result.total_return:.2f}%")
            print(f"   Max Drawdown: {best_result.max_drawdown:.2f}%")
            print(f"   Sharpe Ratio: {best_result.sharpe_ratio:.2f}")
            print(f"   Total Trades: {best_result.total_trades}")
            
            # Validar no conjunto de teste
            print(f"\n🧪 Validando no conjunto de teste...")
            test_result = self.run_backtest(best_thresholds, start_idx=train_size, end_idx=len(self.historical_data))
            
            print(f"📊 Resultado no teste:")
            print(f"   Win Rate: {test_result.win_rate:.1f}%")
            print(f"   Total Return: {test_result.total_return:.2f}%")
            print(f"   Total Trades: {test_result.total_trades}")
            
            return best_thresholds
        else:
            print("❌ Nenhuma configuração válida encontrada")
            return None

    def comprehensive_backtest(self, duration_hours: int = 48):
        """Executa backtest completo com otimização"""
        print(f"🚀 BACKTEST COMPLETO - {duration_hours}h de dados")
        print("=" * 60)
        
        # 1. Coletar dados históricos
        self.collect_historical_data(duration_hours)
        
        # 2. Backtest com thresholds atuais
        print(f"\n📊 TESTE COM THRESHOLDS ATUAIS")
        print("-" * 40)
        
        current_thresholds = {
            'MUITO_ALTA': {'buy': 51, 'sell': 49},
            'ALTA': {'buy': 52, 'sell': 48},
            'MEDIA': {'buy': 53, 'sell': 47},
            'BAIXA': {'buy': 55, 'sell': 45}
        }
        
        current_result = self.run_backtest(current_thresholds)
        self._print_backtest_results("Configuração Atual", current_result)
        
        # 3. Otimizar thresholds
        print(f"\n🎯 OTIMIZAÇÃO DE THRESHOLDS")
        print("-" * 40)
        
        optimized_thresholds = self.optimize_thresholds()
        
        if optimized_thresholds:
            # 4. Backtest com thresholds otimizados
            print(f"\n📈 TESTE COM THRESHOLDS OTIMIZADOS")
            print("-" * 40)
            
            optimized_result = self.run_backtest(optimized_thresholds)
            self._print_backtest_results("Configuração Otimizada", optimized_result)
            
            # 5. Comparação
            print(f"\n⚖️ COMPARAÇÃO DE RESULTADOS")
            print("-" * 40)
            self._compare_results(current_result, optimized_result)
            
            # 6. Salvar configuração otimizada
            self._save_optimized_config(optimized_thresholds)
            
            return optimized_thresholds
        else:
            print("❌ Otimização falhou")
            return current_thresholds

    def _print_backtest_results(self, title: str, result: BacktestResult):
        """Imprime resultados do backtest formatados"""
        print(f"📊 {title}:")
        print(f"   Total de Trades: {result.total_trades}")
        print(f"   Wins: {result.wins} | Losses: {result.losses}")
        print(f"   Win Rate: {result.win_rate:.1f}%")
        print(f"   Total Return: {result.total_return:+.2f}%")
        print(f"   Max Drawdown: {result.max_drawdown:.2f}%")
        print(f"   Sharpe Ratio: {result.sharpe_ratio:.2f}")
        
        if result.confidence_distribution:
            print(f"   Distribuição por Confiança:")
            for conf, count in result.confidence_distribution.items():
                print(f"     {conf}: {count} trades")

    def _compare_results(self, current: BacktestResult, optimized: BacktestResult):
        """Compara resultados atual vs otimizado"""
        improvements = []
        
        if optimized.win_rate > current.win_rate:
            improvements.append(f"Win Rate: {current.win_rate:.1f}% → {optimized.win_rate:.1f}% (+{optimized.win_rate - current.win_rate:.1f}%)")
        
        if optimized.total_return > current.total_return:
            improvements.append(f"Return: {current.total_return:+.2f}% → {optimized.total_return:+.2f}% (+{optimized.total_return - current.total_return:.2f}%)")
        
        if optimized.max_drawdown < current.max_drawdown:
            improvements.append(f"Drawdown: {current.max_drawdown:.2f}% → {optimized.max_drawdown:.2f}% ({optimized.max_drawdown - current.max_drawdown:+.2f}%)")
        
        if optimized.sharpe_ratio > current.sharpe_ratio:
            improvements.append(f"Sharpe: {current.sharpe_ratio:.2f} → {optimized.sharpe_ratio:.2f} (+{optimized.sharpe_ratio - current.sharpe_ratio:.2f})")
        
        if improvements:
            print("✅ Melhorias identificadas:")
            for improvement in improvements:
                print(f"   • {improvement}")
        else:
            print("⚠️ Configuração atual já está bem otimizada")

    def _save_optimized_config(self, thresholds: Dict[str, Dict[str, float]]):
        """Salva configuração otimizada"""
        config_file = 'optimized_thresholds.json'
        
        config_data = {
            'timestamp': datetime.now().isoformat(),
            'thresholds': thresholds,
            'description': 'Thresholds otimizados via backtesting'
        }
        
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"💾 Configuração salva em: {config_file}")

def main():
    """Função principal"""
    optimizer = BacktestingOptimizer()
    
    # Executar backtest completo
    optimized_thresholds = optimizer.comprehensive_backtest(duration_hours=24)
    
    print(f"\n🎉 BACKTEST CONCLUÍDO!")
    print("Thresholds otimizados salvos e prontos para uso.")

if __name__ == "__main__":
    main()
