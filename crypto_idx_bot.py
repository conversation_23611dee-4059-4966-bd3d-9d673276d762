import pandas as pd
import numpy as np
import time
import os
import json
import logging
from datetime import datetime, timedelta, timezone
import pytz
from dotenv import load_dotenv
from binomo_data_provider import BinomoDataProvider
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from dataclasses import dataclass
from typing import Optional, Dict, Tuple

# Carregar variáveis de ambiente
load_dotenv()

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuração da API Binomo
BINOMO_CONFIG = {
    'BINOMO_API_BASE': 'https://api.binomo.com',
    'SYMBOL': 'Z-CRY%2FIDX',
    'TIMEFRAME': '1',
    'LOCALE': 'br',
    'REQUEST_TIMEOUT': 10,
    'MAX_RETRIES': 3,
    'CACHE_DURATION': 30  # Cache por 30 segundos para dados mais dinâmicos
}

# Configurações para análise de resultados
RESULTS_DIR = Path('results')
RESULTS_DIR.mkdir(exist_ok=True)
SIGNALS_FILE = RESULTS_DIR / 'signals_history.json'
ANALYSIS_FILE = RESULTS_DIR / 'strategy_analysis.json'

# Configurações de trading
EXPIRATION_MINUTES = 1  # Tempo de expiração das opções (1 minuto)
PAYOUT_PERCENTAGE = 0.85  # 85% de retorno em caso de acerto

# Configurações de fuso horário - Brasil (São Paulo)
TIMEZONE_SP = pytz.timezone('America/Sao_Paulo')  # Fuso horário de São Paulo (UTC-3/UTC-2 no horário de verão)

def get_current_time_sp():
    """Retorna o horário atual em São Paulo (Brasil)"""
    return datetime.now(TIMEZONE_SP)

def convert_to_sp_time(utc_datetime):
    """Converte datetime UTC para horário de São Paulo (Brasil)"""
    if utc_datetime is None:
        return None
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    return utc_datetime.astimezone(TIMEZONE_SP)

def format_time_sp(dt):
    """Formata datetime para exibição em horário de São Paulo"""
    if dt is None:
        return "N/A"
    if isinstance(dt, str):
        dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
    sp_time = convert_to_sp_time(dt)
    return sp_time.strftime('%d/%m/%Y %H:%M:%S') if sp_time else "N/A"

@dataclass
class TradeSignal:
    """Estrutura para armazenar sinais de trading"""
    entry_time: datetime
    entry_price: float
    direction: str
    score: float
    confidence_level: str
    signals: Dict
    expiration_minutes: int = 1

# ============================
# ESTRATÉGIA OTIMIZADA - PREPARAÇÃO DE FEATURES
# ============================
def prepare_features_optimized(df):
    """Features otimizadas com melhor precisão baseadas no bot_strategy_optimized.py"""
    try:
        if len(df) < 20:
            return None

        df = df.copy()

        # === MÉDIAS MÓVEIS OTIMIZADAS ===
        df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
        df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
        df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
        df['ema_8'] = df['close'].ewm(span=8).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()

        # === RSI OTIMIZADO ===
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
        rs = gain / (loss + 1e-10)  # Evitar divisão por zero
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_smooth'] = df['rsi'].rolling(window=3).mean()  # RSI suavizado

        # === MACD MELHORADO ===
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        df['macd_momentum'] = df['macd_histogram'].diff()  # Momentum do MACD

        # === BOLLINGER BANDS AVANÇADO ===
        df['bb_middle'] = df['close'].rolling(window=20, min_periods=1).mean()
        bb_std = df['close'].rolling(window=20, min_periods=1).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']  # Squeeze indicator

        # === VOLUME INTELIGENTE ===
        if 'tick_volume' not in df.columns:
            df['tick_volume'] = df['volume'] if 'volume' in df.columns else 1000  # Fallback

        df['volume_sma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
        df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
        df['volume_trend'] = df['tick_volume'].rolling(window=5).apply(
            lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1, raw=False
        )
        df['volume_acceleration'] = df['volume_ratio'].diff()  # Aceleração do volume

        # === VOLATILIDADE AVANÇADA ===
        df['atr'] = calculate_atr(df, period=14)  # Average True Range
        df['volatility'] = df['close'].rolling(window=10, min_periods=1).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()

        # === PRESSÃO DE MERCADO MELHORADA ===
        df['body_size'] = abs(df['close'] - df['open']) / df['open']
        df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
        df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']

        # Pressão baseada em candles
        df['bullish_candle'] = (df['close'] > df['open']).astype(int)
        df['bearish_candle'] = (df['close'] < df['open']).astype(int)
        df['doji_candle'] = (abs(df['close'] - df['open']) / df['open'] < 0.001).astype(int)

        # Pressão acumulada
        df['buying_pressure'] = np.where(df['close'] > df['open'],
                                       df['tick_volume'] * df['body_size'], 0)
        df['selling_pressure'] = np.where(df['close'] < df['open'],
                                        df['tick_volume'] * df['body_size'], 0)
        df['net_pressure'] = (df['buying_pressure'] - df['selling_pressure']).rolling(window=5).sum()
        df['pressure_momentum'] = df['net_pressure'].diff()  # Momentum da pressão

        # === MOMENTUM AVANÇADO ===
        df['roc'] = df['close'].pct_change(periods=5) * 100
        df['momentum'] = df['close'] / df['close'].shift(10) - 1
        df['price_acceleration'] = df['close'].diff().diff()  # Segunda derivada do preço

        # === TENDÊNCIA MULTI-TIMEFRAME ===
        df['trend_short'] = np.where(df['ema_8'] > df['ema_21'], 1, -1)
        df['trend_medium'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
        df['trend_long'] = np.where(df['sma_10'] > df['sma_20'], 1, -1)
        df['trend_consensus'] = df['trend_short'] + df['trend_medium'] + df['trend_long']
        df['trend_strength'] = abs(df['ema_8'] - df['ema_21']) / df['ema_21'] * 100

        return df.fillna(0)

    except Exception as e:
        logger.error(f"Erro ao preparar features: {e}")
        return None

def calculate_atr(df, period=14):
    """Calcula Average True Range"""
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())

    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    return true_range.rolling(window=period, min_periods=1).mean()

# ============================
# INDICADORES TÉCNICOS (LEGADOS)
# ============================
def ema(series, period):
    return series.ewm(span=period, adjust=False).mean()

def rsi(series, period=14):
    delta = series.diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)

    avg_gain = pd.Series(gain).rolling(period).mean()
    avg_loss = pd.Series(loss).rolling(period).mean()

    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def detectar_suporte_resistencia(df, janela=20):
    suportes, resistencias = [], []

    for i in range(janela, len(df)):
        min_local = df['low'].iloc[i-janela:i].min()
        max_local = df['high'].iloc[i-janela:i].max()

        if min_local not in suportes:
            suportes.append(min_local)
        if max_local not in resistencias:
            resistencias.append(max_local)

    return suportes, resistencias

# ============================
# DETECÇÃO DE PADRÕES DE VELAS
# ============================
def detectar_padroes(vela_anterior, vela_atual):
    padroes = []

    corpo = abs(vela_atual['close'] - vela_atual['open'])
    pavio_sup = vela_atual['high'] - max(vela_atual['close'], vela_atual['open'])
    pavio_inf = min(vela_atual['close'], vela_atual['open']) - vela_atual['low']

    if vela_atual['close'] > vela_atual['open'] and vela_anterior['close'] < vela_anterior['open']:
        if vela_atual['close'] > vela_anterior['open'] and vela_atual['open'] < vela_anterior['close']:
            padroes.append("Engolfo de Alta")

    if vela_atual['close'] < vela_atual['open'] and vela_anterior['close'] > vela_anterior['open']:
        if vela_atual['close'] < vela_anterior['open'] and vela_atual['open'] > vela_anterior['close']:
            padroes.append("Engolfo de Baixa")

    if corpo <= ((vela_atual['high'] - vela_atual['low']) * 0.1):
        padroes.append("Doji")

    if corpo < (pavio_inf * 0.5) and pavio_inf > (corpo * 2):
        padroes.append("Martelo (possível reversão de alta)")

    if corpo < (pavio_sup * 0.5) and pavio_sup > (corpo * 2):
        padroes.append("Shooting Star (possível reversão de baixa)")

    return padroes

# ============================
# GERAÇÃO DE SINAIS
# ============================
def gerar_sinais_otimizado(df, hora_ultima_vela):
    """
    Gera sinais usando a estratégia otimizada v2.0
    """
    try:
        # Usar estratégia otimizada
        direction, score, signals, confidence_level = advanced_strategy_v2(df)

        if direction is None:
            return []

        # Verificar se é bom horário para trading
        if not is_good_trading_time():
            logger.warning("Horário não recomendado para trading")
            return []

        # Validar consenso dos indicadores
        has_consensus, consensus_ratio = validate_signal_consensus(signals)

        # Filtrar apenas sinais com boa confiança
        if confidence_level in ["BAIXA"] and consensus_ratio < 0.7:
            return []

        # Converter sinais para formato compatível
        confirmacoes = []
        for indicator, signal in signals.items():
            if indicator not in ['volume_penalty']:  # Excluir penalidades
                confirmacoes.append(f"{indicator.replace('_', ' ').title()}: {signal}")

        # Criar sinal otimizado
        sinal = {
            "hora": hora_ultima_vela.strftime("%H:%M"),
            "direcao": direction,
            "confirmacoes": confirmacoes,
            "score": score,
            "confidence_level": confidence_level,
            "consensus_ratio": consensus_ratio,
            "signals_detail": signals
        }

        logger.info(f"Sinal otimizado gerado: {direction} - Score: {score:.2f} - Confiança: {confidence_level}")
        return [sinal]

    except Exception as e:
        logger.error(f"Erro ao gerar sinal otimizado: {e}")
        return []

def gerar_sinais(df, hora_ultima_vela):
    """
    Gera sinais baseados nos indicadores técnicos (versão híbrida: otimizada + legada)
    """
    # Primeiro tentar estratégia otimizada
    sinais_otimizados = gerar_sinais_otimizado(df, hora_ultima_vela)
    if sinais_otimizados:
        return sinais_otimizados

    # Fallback para estratégia legada
    sinais = []

    df['EMA9'] = ema(df['close'], 9)
    df['EMA21'] = ema(df['close'], 21)
    df['EMA50'] = ema(df['close'], 50)
    df['RSI'] = rsi(df['close'])

    suportes, resistencias = detectar_suporte_resistencia(df)

    for i in range(1, len(df)):
        vela_ant = df.iloc[i-1]
        vela_atual = df.iloc[i]
        confirmacoes = []

        if vela_atual['EMA9'] > vela_atual['EMA21'] and vela_atual['EMA21'] > vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Alta (EMAs alinhadas)")
        elif vela_atual['EMA9'] < vela_atual['EMA21'] and vela_atual['EMA21'] < vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Baixa (EMAs alinhadas)")

        if vela_atual['RSI'] < 30:
            confirmacoes.append("RSI Sobrevendido")
        elif vela_atual['RSI'] > 70:
            confirmacoes.append("RSI Sobrecomprado")
        elif 45 < vela_atual['RSI'] < 55:
            confirmacoes.append("RSI Neutro (mercado lateral)")

        padroes = detectar_padroes(vela_ant, vela_atual)
        if padroes:
            confirmacoes.extend(padroes)

        preco = vela_atual['close']
        proximidade_suporte = min([abs(preco - s) for s in suportes]) if suportes else 9999
        proximidade_resistencia = min([abs(preco - r) for r in resistencias]) if resistencias else 9999

        if proximidade_suporte <= 2:
            confirmacoes.append("Próximo de Suporte (possível compra)")
        if proximidade_resistencia <= 2:
            confirmacoes.append("Próximo de Resistência (possível venda)")

        if len(confirmacoes) >= 3:
            direcao = "CALL" if any("Alta" in c or "compra" in c for c in confirmacoes) else "PUT"
            hora_entrada = hora_ultima_vela + timedelta(minutes=i*6)

            sinais.append({
                "hora": hora_entrada.strftime("%H:%M"),
                "direcao": direcao,
                "confirmacoes": confirmacoes
            })

    return sinais

# ============================
# ESTRATÉGIA OTIMIZADA V2.0
# ============================
def validate_signal_consensus(signals):
    """Valida consenso entre indicadores"""
    bullish_signals = 0
    bearish_signals = 0
    neutral_signals = 0

    # Contar sinais de cada direção
    for indicator, signal in signals.items():
        signal_str = str(signal).lower()
        if any(word in signal_str for word in ['bullish', 'oversold', 'recovery', 'buying', 'alta', 'compra']):
            bullish_signals += 1
        elif any(word in signal_str for word in ['bearish', 'overbought', 'selling', 'baixa', 'venda']):
            bearish_signals += 1
        else:
            neutral_signals += 1

    total_signals = bullish_signals + bearish_signals + neutral_signals
    if total_signals == 0:
        return False, 0

    # Calcular consenso (ignorar neutros)
    directional_signals = bullish_signals + bearish_signals
    if directional_signals == 0:
        return False, 0

    consensus_ratio = max(bullish_signals, bearish_signals) / directional_signals
    return consensus_ratio >= 0.6, consensus_ratio

def is_good_trading_time():
    """Verifica se é bom horário para trading (horário de São Paulo/Brasil)"""
    current_time_sp = get_current_time_sp()
    current_hour = current_time_sp.hour
    current_minute = current_time_sp.minute

    # Evitar horários de baixa liquidez (madrugada brasileira)
    if 2 <= current_hour <= 6:
        return False

    # Horários preferenciais para o mercado brasileiro
    # Manhã: 8h às 12h (sobreposição com mercados europeus)
    # Tarde: 14h às 18h (sobreposição com mercados americanos)
    if 8 <= current_hour <= 12 or 14 <= current_hour <= 18:
        return True

    # Horário noturno (19h às 23h) - mercado americano ativo
    if 19 <= current_hour <= 23:
        return True

    # Outros horários são aceitáveis mas não ideais
    return True

def advanced_strategy_v2(df):
    """Estratégia otimizada v2.0 com melhorias implementadas"""
    try:
        features = prepare_features_optimized(df)
        if features is None or len(features) == 0:
            return None, 0, {}, "BAIXA"

        last_row = features.iloc[-1]
        prev_row = features.iloc[-2] if len(features) > 1 else last_row

        # Sistema de pontuação adaptativo
        base_score = 50
        score = base_score
        signals = {}
        confidence_factors = []

        # === 1. ANÁLISE DE TENDÊNCIA (30%) ===
        trend_score = 0

        # Consenso de tendência (peso maior)
        trend_consensus = last_row['trend_consensus']
        if trend_consensus >= 2:  # 2 ou 3 indicadores concordam
            trend_score += 12
            signals['trend_consensus'] = f"Forte Alta ({trend_consensus}/3)"
            confidence_factors.append("TREND_STRONG_UP")
        elif trend_consensus <= -2:
            trend_score -= 12
            signals['trend_consensus'] = f"Forte Baixa ({trend_consensus}/3)"
            confidence_factors.append("TREND_STRONG_DOWN")
        elif trend_consensus == 1:
            trend_score += 6
            signals['trend_consensus'] = f"Moderada Alta ({trend_consensus}/3)"
        elif trend_consensus == -1:
            trend_score -= 6
            signals['trend_consensus'] = f"Moderada Baixa ({trend_consensus}/3)"
        else:
            signals['trend_consensus'] = "Indefinida (0/3)"

        # Força da tendência
        if last_row['trend_strength'] > 0.5:
            trend_bonus = min(6, last_row['trend_strength'] * 2)
            trend_score += trend_bonus if trend_consensus > 0 else -trend_bonus
            signals['trend_strength'] = f"Forte ({last_row['trend_strength']:.2f}%)"
            confidence_factors.append("TREND_STRENGTH")
        else:
            signals['trend_strength'] = f"Fraca ({last_row['trend_strength']:.2f}%)"

        score += trend_score

        # === 2. ANÁLISE DE MOMENTUM (25%) ===
        momentum_score = 0

        # RSI otimizado com zonas específicas
        rsi = last_row['rsi_smooth']
        if rsi < 25:  # Oversold extremo
            momentum_score += 10
            signals['rsi'] = f"Oversold Extremo ({rsi:.1f})"
            confidence_factors.append("RSI_OVERSOLD")
        elif 25 <= rsi <= 35:  # Oversold recovery
            momentum_score += 6
            signals['rsi'] = f"Recovery ({rsi:.1f})"
        elif 65 <= rsi <= 75:  # Overbought entry
            momentum_score -= 6
            signals['rsi'] = f"Overbought ({rsi:.1f})"
        elif rsi > 75:  # Overbought extremo
            momentum_score -= 10
            signals['rsi'] = f"Overbought Extremo ({rsi:.1f})"
            confidence_factors.append("RSI_OVERBOUGHT")
        else:
            signals['rsi'] = f"Neutro ({rsi:.1f})"

        # MACD com momentum
        if (last_row['macd'] > last_row['macd_signal'] and
            last_row['macd_momentum'] > 0):
            momentum_score += 8
            signals['macd'] = f"Bullish + Momentum ({last_row['macd']:.5f})"
            confidence_factors.append("MACD_BULLISH_MOMENTUM")
        elif (last_row['macd'] < last_row['macd_signal'] and
              last_row['macd_momentum'] < 0):
            momentum_score -= 8
            signals['macd'] = f"Bearish + Momentum ({last_row['macd']:.5f})"
            confidence_factors.append("MACD_BEARISH_MOMENTUM")
        else:
            signals['macd'] = f"Neutro ({last_row['macd']:.5f})"

        score += momentum_score

        # === 3. ANÁLISE DE VOLUME (20%) ===
        volume_score = 0

        # Volume com confirmação
        if last_row['volume_ratio'] > 1.5 and last_row['volume_acceleration'] > 0:
            volume_score += 10
            signals['volume'] = f"Alto + Aceleração ({last_row['volume_ratio']:.2f}x)"
            confidence_factors.append("VOLUME_SURGE")
        elif last_row['volume_ratio'] > 1.2:
            volume_score += 6
            signals['volume'] = f"Alto ({last_row['volume_ratio']:.2f}x)"
        elif last_row['volume_ratio'] < 0.6:
            volume_score -= 4
            signals['volume'] = f"Baixo ({last_row['volume_ratio']:.2f}x)"
        else:
            signals['volume'] = f"Normal ({last_row['volume_ratio']:.2f}x)"

        # Penalizar volume muito baixo
        if last_row['volume_ratio'] < 0.5:
            score *= 0.85  # Reduz score em 15%
            signals['volume_penalty'] = "Volume insuficiente"

        score += volume_score

        # === 4. ANÁLISE DE PRESSÃO (15%) ===
        pressure_score = 0

        # Pressão com momentum
        if (last_row['net_pressure'] > 2000 and
            last_row['pressure_momentum'] > 0):
            pressure_score += 8
            signals['pressure'] = f"Forte Compra + Momentum ({last_row['net_pressure']:.0f})"
            confidence_factors.append("PRESSURE_BUYING_SURGE")
        elif (last_row['net_pressure'] < -2000 and
              last_row['pressure_momentum'] < 0):
            pressure_score -= 8
            signals['pressure'] = f"Forte Venda + Momentum ({last_row['net_pressure']:.0f})"
            confidence_factors.append("PRESSURE_SELLING_SURGE")
        elif last_row['net_pressure'] > 500:
            pressure_score += 4
            signals['pressure'] = f"Compra ({last_row['net_pressure']:.0f})"
        elif last_row['net_pressure'] < -500:
            pressure_score -= 4
            signals['pressure'] = f"Venda ({last_row['net_pressure']:.0f})"
        else:
            signals['pressure'] = f"Equilibrada ({last_row['net_pressure']:.0f})"

        score += pressure_score

        # === 5. ANÁLISE DE VOLATILIDADE (10%) ===
        volatility_score = 0

        # Volatilidade ideal para trading
        if 0.8 <= last_row['volatility_ratio'] <= 1.8:
            volatility_score += 5
            signals['volatility'] = f"Ideal ({last_row['volatility_ratio']:.2f}x)"
            confidence_factors.append("VOLATILITY_IDEAL")
        elif last_row['volatility_ratio'] > 3:
            volatility_score -= 5
            signals['volatility'] = f"Muito Alta ({last_row['volatility_ratio']:.2f}x)"
        else:
            signals['volatility'] = f"Normal ({last_row['volatility_ratio']:.2f}x)"

        # Bollinger Bands
        bb_pos = last_row['bb_position']
        if bb_pos < 0.15:  # Próximo da banda inferior
            volatility_score += 3
            signals['bollinger'] = f"Oversold ({bb_pos:.2f})"
        elif bb_pos > 0.85:  # Próximo da banda superior
            volatility_score -= 3
            signals['bollinger'] = f"Overbought ({bb_pos:.2f})"
        else:
            signals['bollinger'] = f"Meio ({bb_pos:.2f})"

        score += volatility_score

        # === DETERMINAÇÃO FINAL COM CONFIANÇA ADAPTATIVA ===

        # Calcular nível de confiança
        high_confidence_count = len([f for f in confidence_factors if f in [
            'TREND_STRONG_UP', 'TREND_STRONG_DOWN', 'RSI_OVERSOLD', 'RSI_OVERBOUGHT',
            'MACD_BULLISH_MOMENTUM', 'MACD_BEARISH_MOMENTUM', 'VOLUME_SURGE',
            'PRESSURE_BUYING_SURGE', 'PRESSURE_SELLING_SURGE', 'VOLATILITY_IDEAL'
        ]])

        if high_confidence_count >= 3:
            confidence_level = "MUITO_ALTA"
            threshold_buy = 51
            threshold_sell = 49
        elif high_confidence_count >= 2:
            confidence_level = "ALTA"
            threshold_buy = 52
            threshold_sell = 48
        elif high_confidence_count >= 1:
            confidence_level = "MEDIA"
            threshold_buy = 53
            threshold_sell = 47
        else:
            confidence_level = "BAIXA"
            threshold_buy = 55
            threshold_sell = 45

        # Determinar direção
        if score > threshold_buy:
            direction = 'CALL'
        elif score < threshold_sell:
            direction = 'PUT'
        else:
            direction = None

        return direction, score, signals, confidence_level

    except Exception as e:
        logger.error(f"Erro na estratégia otimizada: {e}")
        return None, 0, {}, "BAIXA"

# ============================
# OBTENDO DADOS EM TEMPO REAL COM API BINOMO
# ============================
def obter_dados_realtime(count=100, timeframe="1"):
    """
    Obtém dados em tempo real da API Binomo

    Args:
        count: Número de candles para obter (padrão: 100)
        timeframe: Timeframe em minutos ("1", "5", "15")

    Returns:
        DataFrame com dados OHLCV ou None em caso de erro
    """
    try:
        logger.info(f"[INFO] Obtendo dados da API Binomo - {count} candles, timeframe {timeframe}m")

        # Inicializar o provedor de dados
        provider = BinomoDataProvider(BINOMO_CONFIG)

        # Testar conexão
        if not provider.test_connection():
            logger.error("[ERRO] Falha na conexão com a API Binomo")
            return None

        # Obter dados históricos
        df = provider.get_historical_data(timeframe=timeframe, count=count)

        if df is not None and len(df) > 0:
            logger.info(f"[SUCESSO] Obtidos {len(df)} candles da API Binomo")
            logger.info(f"[INFO] Período: {df['timestamp'].min()} até {df['timestamp'].max()}")
            logger.info(f"[INFO] Último preço: {df.iloc[-1]['close']:.5f}")

            # Cleanup
            provider.shutdown()
            return df
        else:
            logger.error("[ERRO] Nenhum dado retornado pela API")
            provider.shutdown()
            return None

    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter dados da API: {e}")
        return None

def obter_preco_atual():
    """
    Obtém o preço atual do ativo

    Returns:
        float: Preço atual ou None em caso de erro
    """
    try:
        provider = BinomoDataProvider(BINOMO_CONFIG)
        preco = provider.get_current_price()
        provider.shutdown()
        return preco
    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter preço atual: {e}")
        return None

def obter_dados_tempo_real_continuo():
    """
    Obtém dados em tempo real de forma contínua (últimas 2 velas)

    Returns:
        DataFrame com as últimas velas ou None em caso de erro
    """
    try:
        provider = BinomoDataProvider(BINOMO_CONFIG)
        df = provider.get_real_time_data()
        provider.shutdown()
        return df
    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter dados em tempo real: {e}")
        return None

# ============================
# SISTEMA DE TRACKING E ANÁLISE DE RESULTADOS
# ============================
def salvar_sinal(sinal, preco_entrada, timestamp_entrada):
    """
    Salva um sinal no arquivo de histórico

    Args:
        sinal: Dicionário com informações do sinal
        preco_entrada: Preço no momento da entrada
        timestamp_entrada: Timestamp da entrada
    """
    try:
        # Carregar histórico existente
        if SIGNALS_FILE.exists():
            with open(SIGNALS_FILE, 'r', encoding='utf-8') as f:
                historico = json.load(f)
        else:
            historico = []

        # Converter timestamps para horário de São Paulo
        timestamp_entrada_sp = convert_to_sp_time(timestamp_entrada)
        timestamp_expiracao_sp = timestamp_entrada_sp + timedelta(minutes=EXPIRATION_MINUTES)

        # Criar registro do sinal
        registro = {
            'id': len(historico) + 1,
            'timestamp_entrada': timestamp_entrada.isoformat(),
            'timestamp_entrada_sp': timestamp_entrada_sp.isoformat(),
            'horario': sinal['hora'],
            'horario_sp': timestamp_entrada_sp.strftime('%H:%M'),
            'direcao': sinal['direcao'],
            'confirmacoes': sinal['confirmacoes'],
            'num_confirmacoes': len(sinal['confirmacoes']),
            'preco_entrada': preco_entrada,
            'timestamp_expiracao': (timestamp_entrada + timedelta(minutes=EXPIRATION_MINUTES)).isoformat(),
            'timestamp_expiracao_sp': timestamp_expiracao_sp.isoformat(),
            'resultado': None,  # Será preenchido depois
            'preco_saida': None,
            'gain_loss': None,
            'payout': None
        }

        historico.append(registro)

        # Salvar arquivo
        with open(SIGNALS_FILE, 'w', encoding='utf-8') as f:
            json.dump(historico, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ Sinal {registro['id']} salvo: {sinal['direcao']} às {sinal['hora']}")
        return registro['id']

    except Exception as e:
        logger.error(f"❌ Erro ao salvar sinal: {e}")
        return None

def verificar_resultados():
    """
    Verifica e atualiza os resultados dos sinais pendentes
    """
    try:
        if not SIGNALS_FILE.exists():
            return

        # Carregar histórico
        with open(SIGNALS_FILE, 'r', encoding='utf-8') as f:
            historico = json.load(f)

        # Obter dados atuais para verificação
        provider = BinomoDataProvider(BINOMO_CONFIG)
        df_atual = provider.get_historical_data(timeframe="1", count=10)
        provider.shutdown()

        if df_atual is None:
            logger.warning("⚠️ Não foi possível obter dados para verificar resultados")
            return

        agora_utc = datetime.now(timezone.utc)
        agora_sp = get_current_time_sp()
        sinais_atualizados = 0

        for sinal in historico:
            # Verificar apenas sinais pendentes
            if sinal['resultado'] is not None:
                continue

            # Usar timestamp de expiração em SP se disponível, senão UTC
            if 'timestamp_expiracao_sp' in sinal:
                timestamp_expiracao = datetime.fromisoformat(sinal['timestamp_expiracao_sp'])
                agora_comparacao = agora_sp
            else:
                timestamp_expiracao = datetime.fromisoformat(sinal['timestamp_expiracao'])
                if timestamp_expiracao.tzinfo is None:
                    timestamp_expiracao = timestamp_expiracao.replace(tzinfo=timezone.utc)
                agora_comparacao = agora_utc

            # Verificar se já expirou
            if agora_comparacao >= timestamp_expiracao:
                # Encontrar o preço de saída mais próximo do horário de expiração
                preco_saida = encontrar_preco_expiracao(df_atual, timestamp_expiracao)

                if preco_saida is not None:
                    # Calcular resultado
                    resultado = calcular_resultado(
                        sinal['direcao'],
                        sinal['preco_entrada'],
                        preco_saida
                    )

                    # Atualizar sinal
                    sinal['preco_saida'] = preco_saida
                    sinal['resultado'] = resultado
                    sinal['gain_loss'] = 'GAIN' if resultado else 'LOSS'
                    sinal['payout'] = PAYOUT_PERCENTAGE if resultado else -1.0

                    sinais_atualizados += 1
                    logger.info(f"📊 Sinal {sinal['id']} atualizado: {sinal['gain_loss']}")

        if sinais_atualizados > 0:
            # Salvar histórico atualizado
            with open(SIGNALS_FILE, 'w', encoding='utf-8') as f:
                json.dump(historico, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ {sinais_atualizados} sinais atualizados")

            # Gerar análise atualizada
            gerar_analise_estrategia()

    except Exception as e:
        logger.error(f"❌ Erro ao verificar resultados: {e}")

def encontrar_preco_expiracao(df, timestamp_expiracao):
    """
    Encontra o preço mais próximo do horário de expiração
    """
    try:
        # Converter timestamp para o mesmo timezone
        if timestamp_expiracao.tzinfo is None:
            timestamp_expiracao = timestamp_expiracao.replace(tzinfo=df.iloc[0]['timestamp'].tzinfo)

        # Encontrar a vela mais próxima
        diferencas = abs(df['timestamp'] - timestamp_expiracao)
        idx_mais_proximo = diferencas.idxmin()

        return float(df.loc[idx_mais_proximo, 'close'])

    except Exception as e:
        logger.error(f"❌ Erro ao encontrar preço de expiração: {e}")
        return None

def calcular_resultado(direcao, preco_entrada, preco_saida):
    """
    Calcula se o sinal foi GAIN ou LOSS

    Args:
        direcao: 'CALL' ou 'PUT'
        preco_entrada: Preço na entrada
        preco_saida: Preço na saída

    Returns:
        bool: True para GAIN, False para LOSS
    """
    if direcao == 'CALL':
        return preco_saida > preco_entrada
    elif direcao == 'PUT':
        return preco_saida < preco_entrada
    else:
        return False

def gerar_analise_estrategia():
    """
    Gera análise completa da estratégia baseada no histórico
    """
    try:
        if not SIGNALS_FILE.exists():
            logger.warning("⚠️ Nenhum histórico de sinais encontrado")
            return

        # Carregar histórico
        with open(SIGNALS_FILE, 'r', encoding='utf-8') as f:
            historico = json.load(f)

        # Filtrar apenas sinais com resultado
        sinais_completos = [s for s in historico if s['resultado'] is not None]

        if not sinais_completos:
            logger.warning("⚠️ Nenhum sinal com resultado encontrado")
            return

        # Calcular estatísticas gerais
        total_sinais = len(sinais_completos)
        gains = sum(1 for s in sinais_completos if s['resultado'])
        losses = total_sinais - gains
        win_rate = (gains / total_sinais) * 100 if total_sinais > 0 else 0

        # Calcular lucro/prejuízo
        lucro_total = sum(s['payout'] for s in sinais_completos)
        lucro_medio = lucro_total / total_sinais if total_sinais > 0 else 0

        # Análise por direção
        calls = [s for s in sinais_completos if s['direcao'] == 'CALL']
        puts = [s for s in sinais_completos if s['direcao'] == 'PUT']

        call_wins = sum(1 for s in calls if s['resultado'])
        put_wins = sum(1 for s in puts if s['resultado'])

        call_win_rate = (call_wins / len(calls)) * 100 if calls else 0
        put_win_rate = (put_wins / len(puts)) * 100 if puts else 0

        # Análise por número de confirmações
        confirmacoes_stats = {}
        for s in sinais_completos:
            num_conf = s['num_confirmacoes']
            if num_conf not in confirmacoes_stats:
                confirmacoes_stats[num_conf] = {'total': 0, 'wins': 0}
            confirmacoes_stats[num_conf]['total'] += 1
            if s['resultado']:
                confirmacoes_stats[num_conf]['wins'] += 1

        # Análise por tipo de confirmação
        tipos_confirmacao = {}
        for s in sinais_completos:
            for conf in s['confirmacoes']:
                if conf not in tipos_confirmacao:
                    tipos_confirmacao[conf] = {'total': 0, 'wins': 0}
                tipos_confirmacao[conf]['total'] += 1
                if s['resultado']:
                    tipos_confirmacao[conf]['wins'] += 1

        # Criar relatório
        agora_sp = get_current_time_sp()
        analise = {
            'timestamp_analise': datetime.now().isoformat(),
            'timestamp_analise_sp': agora_sp.isoformat(),
            'estatisticas_gerais': {
                'total_sinais': total_sinais,
                'gains': gains,
                'losses': losses,
                'win_rate': round(win_rate, 2),
                'lucro_total': round(lucro_total, 4),
                'lucro_medio': round(lucro_medio, 4)
            },
            'analise_por_direcao': {
                'CALL': {
                    'total': len(calls),
                    'wins': call_wins,
                    'win_rate': round(call_win_rate, 2)
                },
                'PUT': {
                    'total': len(puts),
                    'wins': put_wins,
                    'win_rate': round(put_win_rate, 2)
                }
            },
            'analise_por_confirmacoes': {
                str(k): {
                    'total': v['total'],
                    'wins': v['wins'],
                    'win_rate': round((v['wins'] / v['total']) * 100, 2)
                }
                for k, v in confirmacoes_stats.items()
            },
            'tipos_confirmacao_mais_eficazes': {
                conf: {
                    'total': stats['total'],
                    'wins': stats['wins'],
                    'win_rate': round((stats['wins'] / stats['total']) * 100, 2)
                }
                for conf, stats in sorted(
                    tipos_confirmacao.items(),
                    key=lambda x: x[1]['wins'] / x[1]['total'],
                    reverse=True
                )[:10]  # Top 10
            }
        }

        # Salvar análise
        with open(ANALYSIS_FILE, 'w', encoding='utf-8') as f:
            json.dump(analise, f, indent=2, ensure_ascii=False)

        logger.info("📊 Análise da estratégia gerada com sucesso")
        return analise

    except Exception as e:
        logger.error(f"❌ Erro ao gerar análise: {e}")
        return None

def exibir_relatorio_sessao(sinais_tempo_real, total_gains, total_losses):
    """
    Exibe relatório da sessão atual (Modo 2)
    """
    try:
        agora_sp = get_current_time_sp()
        total_operacoes = total_gains + total_losses
        win_rate = (total_gains / total_operacoes * 100) if total_operacoes > 0 else 0
        lucro_total = (total_gains * PAYOUT_PERCENTAGE) - total_losses if total_operacoes > 0 else 0

        print("\n" + "="*60)
        print("📊 RELATÓRIO DA SESSÃO - MODO 2 (TEMPO REAL)")
        print(f"🕐 Finalizada em: {agora_sp.strftime('%d/%m/%Y %H:%M:%S')} (Brasil/SP)")
        print("="*60)

        print(f"\n📈 RESUMO DA SESSÃO:")
        print(f"   🎯 Total de operações: {total_operacoes}")
        print(f"   ✅ Gains: {total_gains}")
        print(f"   ❌ Losses: {total_losses}")
        print(f"   📊 Win Rate: {win_rate:.1f}%")
        print(f"   💰 Lucro/Prejuízo: {lucro_total:+.2f}")

        if sinais_tempo_real:
            print(f"\n📋 DETALHES DOS SINAIS:")
            for sinal in sinais_tempo_real:
                timestamp = datetime.fromisoformat(sinal['timestamp'])
                resultado_texto = "✅ GAIN" if sinal['resultado'] else "❌ LOSS" if sinal['resultado'] is not None else "⏳ PENDENTE"

                print(f"   #{sinal['id']} - {timestamp.strftime('%H:%M')} - {sinal['direcao']} - {resultado_texto}")
                print(f"       Entrada: {sinal['preco_entrada']:.5f} | Confirmações: {sinal['confirmacoes']}")
                if sinal['preco_saida']:
                    print(f"       Saída: {sinal['preco_saida']:.5f} | Diferença: {(sinal['preco_saida'] - sinal['preco_entrada']):+.5f}")

        print("\n" + "="*60)

    except Exception as e:
        logger.error(f"❌ Erro ao exibir relatório da sessão: {e}")

def exibir_relatorio_completo():
    """
    Exibe relatório completo com todas as operações, gains e losses do histórico
    """
    try:
        if not SIGNALS_FILE.exists():
            print("❌ Nenhum histórico de sinais encontrado.")
            print("💡 Execute algumas operações primeiro para gerar dados.")
            return

        # Carregar histórico completo
        with open(SIGNALS_FILE, 'r', encoding='utf-8') as f:
            historico = json.load(f)

        # Verificar resultados pendentes primeiro
        print("🔍 Verificando resultados pendentes...")
        verificar_resultados()

        # Recarregar após verificação
        with open(SIGNALS_FILE, 'r', encoding='utf-8') as f:
            historico = json.load(f)

        # Filtrar sinais com resultado
        sinais_completos = [s for s in historico if s['resultado'] is not None]
        sinais_pendentes = [s for s in historico if s['resultado'] is None]

        agora_sp = get_current_time_sp()

        print("\n" + "="*70)
        print("📊 RELATÓRIO COMPLETO - CRYPTO IDX BOT")
        print(f"🕐 Gerado em: {agora_sp.strftime('%d/%m/%Y %H:%M:%S')} (Brasil/SP)")
        print("="*70)

        # Estatísticas gerais
        total_sinais = len(historico)
        total_completos = len(sinais_completos)
        total_pendentes = len(sinais_pendentes)

        gains = sum(1 for s in sinais_completos if s['resultado'])
        losses = total_completos - gains
        win_rate = (gains / total_completos * 100) if total_completos > 0 else 0

        lucro_total = sum(s['payout'] for s in sinais_completos if 'payout' in s)
        lucro_medio = lucro_total / total_completos if total_completos > 0 else 0

        print(f"\n📈 ESTATÍSTICAS GERAIS:")
        print(f"   📊 Total de sinais gerados: {total_sinais}")
        print(f"   ✅ Operações finalizadas: {total_completos}")
        print(f"   ⏳ Operações pendentes: {total_pendentes}")
        print(f"   🎯 Gains: {gains}")
        print(f"   ❌ Losses: {losses}")
        print(f"   📊 Win Rate: {win_rate:.1f}%")
        print(f"   💰 Lucro total: {lucro_total:+.4f}")
        print(f"   📊 Lucro médio por operação: {lucro_medio:+.4f}")

        # Análise por direção
        calls = [s for s in sinais_completos if s['direcao'] == 'CALL']
        puts = [s for s in sinais_completos if s['direcao'] == 'PUT']

        call_wins = sum(1 for s in calls if s['resultado'])
        put_wins = sum(1 for s in puts if s['resultado'])

        call_win_rate = (call_wins / len(calls)) * 100 if calls else 0
        put_win_rate = (put_wins / len(puts)) * 100 if puts else 0

        print(f"\n📊 ANÁLISE POR DIREÇÃO:")
        print(f"   📈 CALL: {call_wins}/{len(calls)} ({call_win_rate:.1f}%)")
        print(f"   📉 PUT: {put_wins}/{len(puts)} ({put_win_rate:.1f}%)")

        # Análise por período (últimos 7 dias)
        if sinais_completos:
            print(f"\n📅 ANÁLISE TEMPORAL:")

            # Agrupar por data
            sinais_por_data = {}
            for sinal in sinais_completos:
                if 'timestamp_entrada_sp' in sinal:
                    data_sinal = datetime.fromisoformat(sinal['timestamp_entrada_sp']).date()
                else:
                    data_sinal = datetime.fromisoformat(sinal['timestamp_entrada']).date()

                if data_sinal not in sinais_por_data:
                    sinais_por_data[data_sinal] = {'total': 0, 'gains': 0}

                sinais_por_data[data_sinal]['total'] += 1
                if sinal['resultado']:
                    sinais_por_data[data_sinal]['gains'] += 1

            # Mostrar últimos 7 dias
            for data in sorted(sinais_por_data.keys(), reverse=True)[:7]:
                dados = sinais_por_data[data]
                wr = (dados['gains'] / dados['total']) * 100
                print(f"   {data.strftime('%d/%m/%Y')}: {dados['gains']}/{dados['total']} ({wr:.1f}%)")

        # Últimas 10 operações
        if sinais_completos:
            print(f"\n📋 ÚLTIMAS 10 OPERAÇÕES:")
            ultimas_operacoes = sorted(sinais_completos,
                                     key=lambda x: x.get('timestamp_entrada_sp', x['timestamp_entrada']),
                                     reverse=True)[:10]

            for i, sinal in enumerate(ultimas_operacoes, 1):
                if 'timestamp_entrada_sp' in sinal:
                    horario = datetime.fromisoformat(sinal['timestamp_entrada_sp']).strftime('%d/%m %H:%M')
                else:
                    horario = datetime.fromisoformat(sinal['timestamp_entrada']).strftime('%d/%m %H:%M')

                resultado_emoji = "✅" if sinal['resultado'] else "❌"
                lucro_op = sinal.get('payout', 0)

                print(f"   {i:2d}. {horario} | {sinal['direcao']} | {resultado_emoji} | {lucro_op:+.2f}")

        # Operações pendentes
        if sinais_pendentes:
            print(f"\n⏳ OPERAÇÕES PENDENTES ({len(sinais_pendentes)}):")
            for sinal in sinais_pendentes[-5:]:  # Últimas 5 pendentes
                if 'timestamp_entrada_sp' in sinal:
                    horario = datetime.fromisoformat(sinal['timestamp_entrada_sp']).strftime('%d/%m %H:%M')
                else:
                    horario = datetime.fromisoformat(sinal['timestamp_entrada']).strftime('%d/%m %H:%M')

                print(f"   • {horario} | {sinal['direcao']} | Preço: {sinal['preco_entrada']:.5f}")

        print("\n" + "="*70)
        print("💡 Use a opção 5 do menu para verificar resultados pendentes")
        print("="*70)

    except Exception as e:
        logger.error(f"❌ Erro ao exibir relatório completo: {e}")

def exibir_analise_estrategia():
    """
    Exibe a análise da estratégia de forma formatada
    """
    try:
        if not ANALYSIS_FILE.exists():
            print("❌ Nenhuma análise encontrada. Execute alguns sinais primeiro.")
            return

        with open(ANALYSIS_FILE, 'r', encoding='utf-8') as f:
            analise = json.load(f)

        stats = analise['estatisticas_gerais']

        # Mostrar horário da análise em SP
        if 'timestamp_analise_sp' in analise:
            timestamp_sp = datetime.fromisoformat(analise['timestamp_analise_sp'])
            horario_analise = timestamp_sp.strftime('%d/%m/%Y %H:%M:%S')
        else:
            horario_analise = "N/A"

        print("\n" + "="*60)
        print("📊 ANÁLISE DA ESTRATÉGIA CRYPTO IDX")
        print(f"🕐 Horário: {horario_analise} (Brasil/SP)")
        print("="*60)

        print(f"\n📈 ESTATÍSTICAS GERAIS:")
        print(f"   Total de Sinais: {stats['total_sinais']}")
        print(f"   ✅ Gains: {stats['gains']}")
        print(f"   ❌ Losses: {stats['losses']}")
        print(f"   🎯 Win Rate: {stats['win_rate']}%")
        print(f"   💰 Lucro Total: {stats['lucro_total']:.4f}")
        print(f"   📊 Lucro Médio: {stats['lucro_medio']:.4f}")

        # Análise por direção
        print(f"\n📊 ANÁLISE POR DIREÇÃO:")
        for direcao, dados in analise['analise_por_direcao'].items():
            if dados['total'] > 0:
                print(f"   {direcao}: {dados['wins']}/{dados['total']} ({dados['win_rate']}%)")

        # Análise por confirmações
        print(f"\n🔍 ANÁLISE POR NÚMERO DE CONFIRMAÇÕES:")
        for num_conf, dados in analise['analise_por_confirmacoes'].items():
            print(f"   {num_conf} confirmações: {dados['wins']}/{dados['total']} ({dados['win_rate']}%)")

        # Top confirmações
        print(f"\n🏆 TOP 5 CONFIRMAÇÕES MAIS EFICAZES:")
        for i, (conf, dados) in enumerate(list(analise['tipos_confirmacao_mais_eficazes'].items())[:5], 1):
            print(f"   {i}. {conf}")
            print(f"      {dados['wins']}/{dados['total']} ({dados['win_rate']}%)")

        print("\n" + "="*60)

    except Exception as e:
        logger.error(f"❌ Erro ao exibir análise: {e}")

# ============================
# EXEMPLO DE USO EM TEMPO REAL COM API BINOMO
# ============================
def executar_bot_trading_automatico():
    """
    Executa o bot em modo trading automático (MODO 2):
    Analisa → Sinal → Aguarda → Resultado → Repete
    Envia sinais em tempo real a partir do minuto corrente (sem histórico)
    """
    agora_inicio = get_current_time_sp()
    logger.info("🚀 === INICIANDO BOT TRADING AUTOMÁTICO - MODO 2 ===")
    logger.info(f"🕐 Iniciado em: {agora_inicio.strftime('%d/%m/%Y %H:%M:%S')} (Brasil/SP)")
    logger.info("📊 Modo: Análise → Sinal → Aguarda → Resultado → Repete")
    logger.info("⏱️ Tempo de expiração: 1 minuto")
    logger.info("🔄 Sinais em tempo real (sem histórico)")
    logger.info("🛑 Pressione Ctrl+C para parar")

    ciclo = 0
    total_gains = 0
    total_losses = 0
    sinais_tempo_real = []  # Lista para armazenar apenas sinais da sessão atual

    while True:
        try:
            ciclo += 1
            timestamp_ciclo_sp = get_current_time_sp()

            print("\n" + "🔄" + "="*60 + "🔄")
            print(f"🎯 CICLO #{ciclo} - {timestamp_ciclo_sp.strftime('%H:%M:%S')} (Brasil/SP)")
            print("="*64)

            # ETAPA 1: Análise do mercado em tempo real
            print("📊 ETAPA 1: Analisando mercado em tempo real...")
            df = obter_dados_realtime(count=50, timeframe="1")  # Reduzido para análise mais rápida

            if df is None or len(df) == 0:
                print("❌ Falha ao obter dados. Aguardando 30s...")
                time.sleep(30)
                continue

            # Usar apenas dados do minuto corrente
            agora_sp = get_current_time_sp()
            minuto_atual = agora_sp.replace(second=0, microsecond=0)

            # Filtrar dados para análise em tempo real (últimas 2 velas)
            df_tempo_real = df.tail(2).copy()
            hora_ultima_vela = df_tempo_real.iloc[-1]['timestamp']
            preco_atual = df_tempo_real.iloc[-1]['close']
            variacao = ((df_tempo_real.iloc[-1]['close'] - df_tempo_real.iloc[-2]['close']) / df_tempo_real.iloc[-2]['close']) * 100

            print(f"💰 Preço atual: {preco_atual:.5f}")
            print(f"📈 Variação: {variacao:+.2f}%")
            print(f"🕐 Minuto atual: {minuto_atual.strftime('%H:%M')} (Brasil/SP)")

            # ETAPA 2: Gerar sinais em tempo real
            print("\n🎯 ETAPA 2: Gerando sinais em tempo real...")
            sinais = gerar_sinais(df, hora_ultima_vela)

            if not sinais:
                print("❌ Nenhum sinal encontrado. Aguardando próximo minuto...")
                # Aguardar até o próximo minuto
                segundos_para_proximo_minuto = 60 - agora_sp.second
                time.sleep(min(segundos_para_proximo_minuto, 30))
                continue

            # Pegar apenas o primeiro sinal (mais forte)
            sinal_principal = sinais[0]

            # Adicionar timestamp do minuto corrente
            sinal_principal['timestamp_real'] = minuto_atual.isoformat()
            sinal_principal['preco_entrada'] = preco_atual

            print(f"🚨 SINAL EM TEMPO REAL DETECTADO!")
            print(f"⏰ Horário: {minuto_atual.strftime('%H:%M:%S')} (Brasil/SP)")
            print(f"📈 Direção: {sinal_principal['direcao']}")
            print(f"💰 Preço entrada: {preco_atual:.5f}")

            # Mostrar apenas as 3 principais confirmações para não poluir
            confirmacoes_principais = sinal_principal['confirmacoes'][:3]
            print(f"✅ Principais confirmações ({len(confirmacoes_principais)}):")
            for conf in confirmacoes_principais:
                print(f"   • {conf}")

            if len(sinal_principal['confirmacoes']) > 3:
                print(f"   ... e mais {len(sinal_principal['confirmacoes']) - 3} confirmações")

            # Adicionar à lista de sinais da sessão (não salvar em arquivo)
            sinal_sessao = {
                'id': len(sinais_tempo_real) + 1,
                'timestamp': minuto_atual.isoformat(),
                'direcao': sinal_principal['direcao'],
                'preco_entrada': preco_atual,
                'confirmacoes': len(sinal_principal['confirmacoes']),
                'resultado': None,
                'preco_saida': None
            }
            sinais_tempo_real.append(sinal_sessao)

            print(f"📝 Sinal #{sinal_sessao['id']} registrado para sessão atual")

            # ETAPA 3: Aguardar expiração (1 minuto)
            print(f"\n⏳ ETAPA 3: Aguardando expiração ({EXPIRATION_MINUTES} minuto)...")
            print(f"🕐 Entrada: {preco_atual:.5f} ({sinal_principal['direcao']})")

            # Countdown mais eficiente (atualiza a cada 15 segundos)
            for segundos_restantes in range(EXPIRATION_MINUTES * 60, 0, -15):
                minutos = segundos_restantes // 60
                segundos = segundos_restantes % 60
                print(f"⏰ Tempo restante: {minutos:02d}:{segundos:02d}", end="\r")
                time.sleep(15)

            print("\n🔔 Tempo expirado! Verificando resultado...")

            # ETAPA 4: Verificar resultado
            print("\n📊 ETAPA 4: Verificando resultado...")

            # Obter preço atual para verificação
            df_resultado = obter_dados_realtime(count=3, timeframe="1")
            if df_resultado is not None and len(df_resultado) > 0:
                preco_saida = df_resultado.iloc[-1]['close']

                # Calcular resultado
                if sinal_principal['direcao'] == 'CALL':
                    resultado = preco_saida > preco_atual
                else:  # PUT
                    resultado = preco_saida < preco_atual

                # Atualizar sinal da sessão
                sinal_sessao['resultado'] = resultado
                sinal_sessao['preco_saida'] = preco_saida

                # Atualizar estatísticas
                if resultado:
                    total_gains += 1
                    resultado_texto = "✅ GAIN"
                    lucro = PAYOUT_PERCENTAGE
                else:
                    total_losses += 1
                    resultado_texto = "❌ LOSS"
                    lucro = -1.0

                print(f"🎯 RESULTADO: {resultado_texto}")
                print(f"💰 Preço entrada: {preco_atual:.5f}")
                print(f"💰 Preço saída: {preco_saida:.5f}")
                print(f"📊 Diferença: {(preco_saida - preco_atual):+.5f}")
                print(f"💵 Lucro/Prejuízo: {lucro:+.2f}")

            else:
                print("❌ Erro ao obter dados para verificação")
                sinal_sessao['resultado'] = None

            # ETAPA 5: Estatísticas da sessão atual
            total_operacoes = total_gains + total_losses
            win_rate = (total_gains / total_operacoes * 100) if total_operacoes > 0 else 0

            print(f"\n📈 ESTATÍSTICAS DA SESSÃO ATUAL:")
            print(f"🎯 Total de operações: {total_operacoes}")
            print(f"✅ Gains: {total_gains}")
            print(f"❌ Losses: {total_losses}")
            print(f"📊 Win Rate: {win_rate:.1f}%")

            if total_operacoes > 0:
                lucro_total = (total_gains * PAYOUT_PERCENTAGE) - total_losses
                print(f"💰 Lucro da sessão: {lucro_total:+.2f}")

            print(f"\n🔄 Aguardando próximo minuto...")

            # Aguardar até o próximo minuto para sincronizar
            agora = get_current_time_sp()
            segundos_para_proximo_minuto = 60 - agora.second
            time.sleep(min(segundos_para_proximo_minuto + 5, 30))  # +5s para garantir novo minuto

        except KeyboardInterrupt:
            print("\n\n🛑 BOT INTERROMPIDO PELO USUÁRIO")
            print("📊 Gerando relatório da sessão...")

            # Exibir relatório da sessão atual
            exibir_relatorio_sessao(sinais_tempo_real, total_gains, total_losses)

            print("\n✅ Sessão finalizada com sucesso!")
            break

        except Exception as e:
            print(f"\n❌ ERRO NO CICLO: {e}")
            print("🔄 Tentando novamente em 30 segundos...")
            time.sleep(30)

def executar_bot_tempo_real(intervalo_atualizacao=60):
    """
    Executa o bot em tempo real com atualizações periódicas (modo antigo)

    Args:
        intervalo_atualizacao: Intervalo em segundos entre atualizações
    """
    logger.info("=== INICIANDO BOT CRYPTO IDX - TEMPO REAL ===")
    logger.info(f"Intervalo de atualização: {intervalo_atualizacao} segundos")
    logger.info("💾 Sinais serão salvos automaticamente para tracking")

    ciclo = 0

    while True:
        try:
            ciclo += 1
            logger.info("\n" + "="*50)
            logger.info(f"ANÁLISE #{ciclo} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("="*50)

            # Verificar resultados pendentes a cada 5 ciclos
            if ciclo % 5 == 0:
                logger.info("🔍 Verificando resultados pendentes...")
                verificar_resultados()

            # Obter dados em tempo real
            df = obter_dados_realtime(count=100, timeframe="1")

            if df is None or len(df) == 0:
                logger.error("Falha ao obter dados. Tentando novamente em 30 segundos...")
                time.sleep(30)
                continue

            # Usar a timestamp da última vela
            hora_ultima_vela = df.iloc[-1]['timestamp']
            preco_atual = df.iloc[-1]['close']

            # Gerar sinais
            sinais = gerar_sinais(df, hora_ultima_vela)

            # Salvar sinais encontrados
            sinais_salvos = 0
            if sinais:
                for sinal in sinais:
                    sinal_id = salvar_sinal(sinal, preco_atual, hora_ultima_vela)
                    if sinal_id:
                        sinais_salvos += 1

            # Exibir resultados
            if sinais:
                logger.info(f"\n🎯 SINAIS ENCONTRADOS ({len(sinais)}):")
                logger.info(f"💾 SINAIS SALVOS: {sinais_salvos}")
                for i, sinal in enumerate(sinais, 1):
                    logger.info(f"\n--- SINAL {i} ---")
                    logger.info(f"⏰ Horário: {sinal['hora']}")
                    logger.info(f"📈 Direção: {sinal['direcao']}")
                    logger.info(f"✅ Confirmações ({len(sinal['confirmacoes'])}):")
                    for conf in sinal['confirmacoes']:
                        logger.info(f"   • {conf}")
            else:
                logger.info("❌ Nenhum sinal encontrado nesta análise")

            # Exibir informações do mercado
            variacao = ((df.iloc[-1]['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close']) * 100
            logger.info(f"\n📊 INFORMAÇÕES DO MERCADO:")
            logger.info(f"💰 Preço atual: {preco_atual:.5f}")
            logger.info(f"📈 Variação última vela: {variacao:+.2f}%")
            logger.info(f"📅 Última atualização: {hora_ultima_vela.strftime('%H:%M:%S')}")

            # Aguardar próxima atualização
            logger.info(f"\n⏳ Próxima análise em {intervalo_atualizacao} segundos...")
            time.sleep(intervalo_atualizacao)

        except KeyboardInterrupt:
            logger.info("\n🛑 Bot interrompido pelo usuário")
            # Verificar resultados uma última vez antes de sair
            logger.info("🔍 Verificação final de resultados...")
            verificar_resultados()
            break
        except Exception as e:
            logger.error(f"❌ Erro durante execução: {e}")
            logger.info("Tentando novamente em 30 segundos...")
            time.sleep(30)

def executar_analise_unica(salvar_sinais=True):
    """
    Executa uma análise única (sem loop contínuo)

    Args:
        salvar_sinais: Se True, salva os sinais para tracking
    """
    logger.info("=== ANÁLISE ÚNICA - CRYPTO IDX ===")

    # Verificar resultados pendentes primeiro
    if salvar_sinais:
        verificar_resultados()

    # Obter dados
    df = obter_dados_realtime(count=100, timeframe="1")

    if df is None or len(df) == 0:
        logger.error("❌ Falha ao obter dados da API")
        return

    # Usar a timestamp da última vela
    hora_ultima_vela = df.iloc[-1]['timestamp']
    preco_atual = df.iloc[-1]['close']

    # Gerar sinais
    sinais = gerar_sinais(df, hora_ultima_vela)

    # Salvar sinais se solicitado
    sinais_salvos = 0
    if salvar_sinais and sinais:
        for sinal in sinais:
            sinal_id = salvar_sinal(sinal, preco_atual, hora_ultima_vela)
            if sinal_id:
                sinais_salvos += 1

    # Exibir resultados
    print("\n" + "="*60)
    print("📊 RESULTADO DA ANÁLISE")
    print("="*60)

    if sinais:
        print(f"\n🎯 SINAIS ENCONTRADOS: {len(sinais)}")
        if salvar_sinais:
            print(f"💾 SINAIS SALVOS: {sinais_salvos}")

        for i, sinal in enumerate(sinais, 1):
            print(f"\n--- SINAL {i} ---")
            print(f"⏰ Horário: {sinal['hora']}")
            print(f"📈 Direção: {sinal['direcao']}")
            print(f"✅ Confirmações ({len(sinal['confirmacoes'])}):")
            for conf in sinal['confirmacoes']:
                print(f"   • {conf}")
    else:
        print("\n❌ Nenhum sinal encontrado")

    # Informações do mercado
    variacao = ((df.iloc[-1]['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close']) * 100
    print(f"\n📊 INFORMAÇÕES DO MERCADO:")
    print(f"💰 Preço atual: {preco_atual:.5f}")
    print(f"📈 Variação última vela: {variacao:+.2f}%")
    print(f"📅 Dados de: {df.iloc[0]['timestamp'].strftime('%H:%M')} até {df.iloc[-1]['timestamp'].strftime('%H:%M')}")
    print(f"📈 Total de velas analisadas: {len(df)}")

    if salvar_sinais and sinais_salvos > 0:
        print(f"\n💾 Os sinais foram salvos e serão verificados automaticamente após {EXPIRATION_MINUTES} minuto(s)")
        print(f"📁 Arquivo: {SIGNALS_FILE}")

    return sinais

if __name__ == "__main__":
    # Mostrar informações de fuso horário
    agora_sp = get_current_time_sp()

    print("🚀 BOT CRYPTO IDX - BINOMO API")
    print("="*50)
    print(f"🕐 Horário atual: {agora_sp.strftime('%d/%m/%Y %H:%M:%S')} (Brasil/SP)")
    print(f"🌎 Fuso horário: {TIMEZONE_SP}")
    print("="*50)
    print("1. Análise única (com tracking)")
    print("2. 🎯 TRADING AUTOMÁTICO - MODO 2 (Tempo Real)")
    print("3. Execução contínua (modo antigo)")
    print("4. Teste de conexão")
    print("5. Verificar resultados pendentes")
    print("6. Exibir análise da estratégia")
    print("7. Análise única (sem salvar)")
    print("8. 📊 RELATÓRIO COMPLETO (Gains/Losses)")
    print("="*50)
    print("💡 Modo 2: Analisa → Sinal → Aguarda → Resultado → Repete")
    print("🔄 Sinais em tempo real sem histórico")
    print("🇧🇷 Todos os horários são exibidos em horário de São Paulo")

    try:
        opcao = input("\nEscolha uma opção (1-8): ").strip()

        if opcao == "1":
            executar_analise_unica(salvar_sinais=True)
        elif opcao == "2":
            print("\n🎯 INICIANDO TRADING AUTOMÁTICO - MODO 2")
            print("🔄 Modo: Sinais em tempo real (sem histórico)")
            print("⏱️  Análise a cada minuto com sinais instantâneos")
            print("🛑 Pressione Ctrl+C para parar a qualquer momento")

            confirmacao = input("\nDeseja continuar? (s/N): ").strip().lower()
            if confirmacao in ['s', 'sim', 'y', 'yes']:
                executar_bot_trading_automatico()
            else:
                print("❌ Operação cancelada")
        elif opcao == "3":
            intervalo = input("Intervalo de atualização em segundos (padrão: 60): ").strip()
            intervalo = int(intervalo) if intervalo.isdigit() else 60
            executar_bot_tempo_real(intervalo)
        elif opcao == "4":
            logger.info("🔧 Testando conexão com API Binomo...")
            provider = BinomoDataProvider(BINOMO_CONFIG)
            if provider.test_connection():
                logger.info("✅ Conexão bem-sucedida!")
                preco = provider.get_current_price()
                if preco:
                    logger.info(f"💰 Preço atual: {preco:.5f}")
            else:
                logger.error("❌ Falha na conexão")
            provider.shutdown()
        elif opcao == "5":
            print("🔍 Verificando resultados pendentes...")
            verificar_resultados()
            print("✅ Verificação concluída")
        elif opcao == "6":
            exibir_analise_estrategia()
        elif opcao == "7":
            executar_analise_unica(salvar_sinais=False)
        elif opcao == "8":
            exibir_relatorio_completo()
        else:
            print("❌ Opção inválida")

    except KeyboardInterrupt:
        print("\n🛑 Programa interrompido")
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
