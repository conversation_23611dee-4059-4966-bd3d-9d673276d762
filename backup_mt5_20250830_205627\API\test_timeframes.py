#!/usr/bin/env python3
"""Test different timeframes with Binomo API"""

import requests
import json

def test_timeframe(tf):
    url = f'https://api.binomo.com/candles/v1/Z-CRY%2FIDX/2025-08-28T17:00:00/{tf}?locale=br'
    print(f"Testing timeframe {tf}: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            result = data.get('data', [])
            print(f"  ✅ Success: {len(result)} candles")
            if result:
                print(f"  📊 Sample: {result[0]}")
        else:
            print(f"  ❌ HTTP {response.status_code}")
    except Exception as e:
        print(f"  ❌ Error: {e}")
    print()

if __name__ == "__main__":
    print("🧪 Testing Binomo API Timeframes")
    print("=" * 40)
    
    # Test common timeframes
    timeframes = ["1", "5", "15", "30", "60", "240", "1440"]
    for tf in timeframes:
        test_timeframe(tf)