import json
import requests


def getCandlesBrute(url):
    """Get candles from Binomo API with improved error handling"""
    try:
        print(f"Making request to: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict) and "data" in data:
                    return data["data"]
                else:
                    print(f"Unexpected response format: {data}")
                    return data  # Return raw data for analysis
                    
            except ValueError as e:
                print(f"Erro ao converter resposta em JSON: {e}")
                print(f"Raw response: {response.text[:500]}...")  # First 500 chars
                return None
        else:
            print(f"Falha na requisição. Status code: {response.status_code}")
            print(f"Response text: {response.text[:500]}...")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

if __name__ == "__main__":
    link = 'https://api.binomo.com/candles/v1/Z-CRY%2FIDX/2024-09-08T18:00:00/5?locale=br'
    result = getCandlesBrute(link)
    print(f"Result type: {type(result)}")
    print(f"Result: {result}")