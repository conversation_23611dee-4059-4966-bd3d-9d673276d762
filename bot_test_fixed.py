#!/usr/bin/env python3
"""Bot Binomo - Versão Corrigida com Controle de Estado"""

import telebot
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from binomo_data_provider import BinomoDataProvider
import json
from typing import Optional

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    operation: str
    probability: float
    expiration_time: datetime = None
    exit_price: float = None
    result: str = None

class TradingBot:
    def __init__(self):
        print("Inicializando TradingBot...")
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False
        
        # Inicializar <PERSON>
        self.data_provider = BinomoDataProvider(self.config)
        print("TradingBot inicializado com sucesso!")

    def _load_config(self):
        """Carrega variáveis do .env e config_binomo.json"""
        load_dotenv()
        
        # Configurações básicas do .env
        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
        }
        
        # Carregar configurações da API Binomo
        try:
            with open('config_binomo.json', 'r') as f:
                binomo_config = json.load(f)
                self.config.update(binomo_config)
        except FileNotFoundError:
            # Configurações padrão
            self.config.update({
                'BINOMO_API_BASE': 'https://api.binomo.com',
                'SYMBOL': 'Z-CRY%2FIDX',
                'TIMEFRAME': '1',
                'LOCALE': 'br',
                'REQUEST_TIMEOUT': 10,
                'MAX_RETRIES': 3,
                'CACHE_DURATION': 60
            })

    def gerar_sinal(self):
        """Gera sinal com controle de estado correto"""
        # Verifica se há trade ativo aguardando resultado
        if self.active_trade and datetime.now() < self.active_trade.expiration_time:
            remaining_time = (self.active_trade.expiration_time - datetime.now()).total_seconds()
            return f"⏳ Trade ativo! Aguarde {remaining_time/60:.1f} minutos para o resultado."
        
        # Se há trade expirado, calcula resultado primeiro
        if self.active_trade and datetime.now() >= self.active_trade.expiration_time:
            self._calculate_and_send_result()
            self.active_trade = None  # Limpa trade após resultado
        
        # Evita processamento concorrente de sinais
        if self.processing_signal:
            return "⚠️ Aguarde o fim do sinal atual."

        self.processing_signal = True
        try:
            # Simular análise (para teste)
            operation = '🟥VENDA'  # Fixo para teste
            probability = 55.0
            
            # Obter preço de entrada
            entry_price = self.data_provider.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(minutes=3)  # M3
            self.active_trade = TradeResult(entry_time, entry_price, operation, probability, expiration_time=expiration_time)

            # Enviar sinal
            message = (
                f"🎯 **SINAL DE {operation}**\n"
                f"📈 Ativo: {self.config.get('SYMBOL', 'CRY/IDX')}\n"
                f"⏳ Expiração: {expiration_time.strftime('%H:%M:%S')} (M3)\n\n"
                f"📊 Confiança: {probability:.2f}%\n"
                f"💰 Entrada: {entry_price:.5f}"
            )

            self.bot.send_message(self.config['chat_id'], message)
            logging.info(f"Sinal enviado: {operation} - Expiração: {expiration_time.strftime('%H:%M:%S')}")
            
            # Sinal enviado com sucesso, aguardará expiração para calcular resultado
            self.processing_signal = False
            return f"✅ Sinal {operation} enviado! Resultado em 3 minutos."

        except Exception as e:
            logging.error(f"Erro ao gerar sinal: {str(e)}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."
    
    def _calculate_and_send_result(self):
        """Calcula e envia o resultado do trade ativo"""
        if not self.active_trade:
            return
        
        try:
            # Obter preço de fechamento
            exit_price = self.data_provider.get_current_price()
            if exit_price is None:
                logging.error("Erro ao obter preço de fechamento para resultado")
                return
            
            # Calcular resultado
            if self.active_trade.operation == '🟩COMPRA':
                is_win = exit_price > self.active_trade.entry_price
            else:  # VENDA
                is_win = exit_price < self.active_trade.entry_price
            
            result = "✅GAIN 📈" if is_win else "❌LOSS 📉"
            price_change = abs(exit_price - self.active_trade.entry_price) / self.active_trade.entry_price * 100
            
            # Enviar resultado
            result_message = (
                f"🔄 **RESULTADO DO TRADE**\n"
                f"Operação: {self.active_trade.operation}\n"
                f"Entrada: {self.active_trade.entry_price:.5f}\n"
                f"Saída: {exit_price:.5f}\n"
                f"Variação: {price_change:.2f}%\n"
                f"Resultado: {result}"
            )
            
            self.bot.send_message(self.config['chat_id'], result_message)
            logging.info(f"Resultado calculado: {result} - Variação: {price_change:.2f}%")
            
        except Exception as e:
            logging.error(f"Erro ao calcular resultado: {e}")

    def _setup_telegram_handlers(self):
        """Configura comandos no Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            self.bot.send_message(message.chat.id, "🤖 Bot de Trading Ativo! Use /sinal para obter um sinal.")

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal()
            if isinstance(sinal, str):
                self.bot.send_message(message.chat.id, sinal)

    def run(self):
        """Executa o bot"""
        # Testa conexão com API Binomo
        if not self.data_provider.test_connection():
            logging.error("Erro ao conectar com API Binomo")
            return
        logging.info("🚀 Bot de trading iniciado com API Binomo!")
        self.bot.polling()

if __name__ == "__main__":
    print("Iniciando bot...")
    bot = TradingBot()
    bot.run()
