#!/usr/bin/env python3
"""
Bot Telegram Simplificado para Teste de Botões
"""

import telebot
from telebot import types
import os
from datetime import datetime
from dotenv import load_dotenv
import logging

# Carregar variáveis de ambiente
load_dotenv()

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleTelegramBot:
    def __init__(self):
        self.token = os.getenv('TELEGRAM_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        if not self.token:
            raise ValueError("TELEGRAM_TOKEN deve estar configurado no .env")
        
        self.bot = telebot.TeleBot(self.token)
        self.setup_handlers()
        
        logger.info("🚀 Bot Telegram Simples inicializado!")

    def create_test_keyboard(self):
        """Cria teclado de teste"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        btn1 = types.InlineKeyboardButton("✅ Teste 1", callback_data="test1")
        btn2 = types.InlineKeyboardButton("📊 Teste 2", callback_data="test2")
        btn3 = types.InlineKeyboardButton("🎯 Teste 3", callback_data="test3")
        btn4 = types.InlineKeyboardButton("🔄 Menu", callback_data="menu")
        
        keyboard.row(btn1, btn2)
        keyboard.row(btn3)
        keyboard.row(btn4)
        
        return keyboard

    def setup_handlers(self):
        """Configura os handlers do bot"""
        
        @self.bot.message_handler(commands=['start'])
        def start_command(message):
            logger.info(f"Comando /start recebido de {message.from_user.id}")
            
            welcome_text = (
                "🤖 **BOT TESTE - BOTÕES**\n\n"
                "Este é um bot de teste para verificar se os botões funcionam.\n\n"
                "Clique nos botões abaixo:"
            )
            
            try:
                self.bot.send_message(
                    message.chat.id, 
                    welcome_text, 
                    reply_markup=self.create_test_keyboard(),
                    parse_mode='Markdown'
                )
                logger.info("Mensagem de boas-vindas enviada com sucesso")
            except Exception as e:
                logger.error(f"Erro ao enviar mensagem: {e}")
                # Fallback sem markdown
                self.bot.send_message(
                    message.chat.id, 
                    "BOT TESTE - BOTOES\n\nClique nos botoes abaixo:", 
                    reply_markup=self.create_test_keyboard()
                )

        @self.bot.callback_query_handler(func=lambda call: True)
        def callback_handler(call):
            logger.info(f"📞 Callback recebido: '{call.data}' de usuário {call.from_user.id}")
            
            try:
                chat_id = call.message.chat.id
                message_id = call.message.message_id
                
                if call.data == "test1":
                    response_text = "✅ **TESTE 1 FUNCIONOU!**\n\nBotão 1 foi clicado com sucesso!"
                    
                elif call.data == "test2":
                    response_text = "📊 **TESTE 2 FUNCIONOU!**\n\nBotão 2 foi clicado com sucesso!"
                    
                elif call.data == "test3":
                    response_text = "🎯 **TESTE 3 FUNCIONOU!**\n\nBotão 3 foi clicado com sucesso!"
                    
                elif call.data == "menu":
                    response_text = "🔄 **MENU PRINCIPAL**\n\nVoltando ao menu inicial..."
                    
                else:
                    response_text = f"❓ **COMANDO DESCONHECIDO**\n\nCallback: {call.data}"
                
                # Tentar editar mensagem
                try:
                    self.bot.edit_message_text(
                        response_text,
                        chat_id, 
                        message_id,
                        reply_markup=self.create_test_keyboard(),
                        parse_mode='Markdown'
                    )
                    logger.info(f"Mensagem editada com sucesso para callback: {call.data}")
                    
                except Exception as e:
                    logger.error(f"Erro ao editar mensagem: {e}")
                    # Fallback: enviar nova mensagem
                    self.bot.send_message(
                        chat_id,
                        response_text.replace('**', '').replace('*', ''),
                        reply_markup=self.create_test_keyboard()
                    )
                
                # Responder ao callback
                self.bot.answer_callback_query(call.id, f"Processado: {call.data}")
                logger.info(f"Callback {call.data} processado com sucesso")
                
            except Exception as e:
                logger.error(f"❌ Erro no callback handler: {e}")
                try:
                    self.bot.answer_callback_query(call.id, f"Erro: {str(e)[:50]}")
                except:
                    pass

        @self.bot.message_handler(func=lambda message: True)
        def echo_handler(message):
            logger.info(f"Mensagem recebida: {message.text} de {message.from_user.id}")
            
            if message.text.lower() in ['menu', 'start', 'botoes']:
                self.bot.send_message(
                    message.chat.id,
                    "🔄 Menu de teste:",
                    reply_markup=self.create_test_keyboard()
                )
            else:
                self.bot.reply_to(
                    message, 
                    f"Você disse: {message.text}\n\nUse /start para ver os botões"
                )

    def run(self):
        """Executa o bot"""
        logger.info("🚀 Iniciando Bot Telegram Simples...")
        logger.info(f"📱 Token configurado: {self.token[:10]}...")
        
        if self.chat_id:
            logger.info(f"📱 Chat ID configurado: {self.chat_id}")
            
            # Enviar mensagem de inicialização
            try:
                startup_msg = (
                    "🚀 **BOT TESTE ATIVO!**\n\n"
                    f"Iniciado em: {datetime.now().strftime('%H:%M:%S')}\n\n"
                    "Use /start para testar os botões!"
                )
                
                self.bot.send_message(self.chat_id, startup_msg, parse_mode='Markdown')
                logger.info("✅ Mensagem de inicialização enviada")
                
            except Exception as e:
                logger.error(f"❌ Erro ao enviar mensagem de inicialização: {e}")
                try:
                    # Fallback sem markdown
                    self.bot.send_message(self.chat_id, "BOT TESTE ATIVO!\n\nUse /start para testar os botoes!")
                except Exception as e2:
                    logger.error(f"❌ Erro no fallback: {e2}")
        
        # Iniciar polling
        logger.info("📡 Bot em execução... Pressione Ctrl+C para parar")
        try:
            self.bot.polling(none_stop=True, interval=1, timeout=20)
        except Exception as e:
            logger.error(f"❌ Erro no polling: {e}")

if __name__ == "__main__":
    try:
        bot = SimpleTelegramBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n🛑 Bot interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        logger.error(f"Erro fatal: {e}")
