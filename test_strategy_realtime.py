#!/usr/bin/env python3
"""
Teste Completo da Estratégia com Dados em Tempo Real
Valida indicadores técnicos e otimiza estratégia de trading
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime
from binomo_realtime_provider import BinomoRealtimeProvider
import matplotlib.pyplot as plt
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class StrategyTester:
    def __init__(self):
        # Carregar configuração
        with open('config_binomo.json', 'r') as f:
            self.config = json.load(f)
        
        # Inicializar provedor de dados
        self.data_provider = BinomoRealtimeProvider(self.config)
        
        # Histórico de sinais para backtesting
        self.signals_history = []
        self.trades_history = []
        
    def prepare_features_advanced(self, df):
        """Prepara features avançadas para análise técnica"""
        try:
            if len(df) < 20:
                return None

            df = df.copy()
            
            # === MÉDIAS MÓVEIS ===
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
            df['ema_5'] = df['close'].ewm(span=5).mean()
            df['ema_10'] = df['close'].ewm(span=10).mean()
            
            # === INDICADORES DE TENDÊNCIA ===
            df['trend_sma'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            df['trend_ema'] = np.where(df['ema_5'] > df['ema_10'], 1, -1)
            df['trend_strength'] = abs(df['sma_5'] - df['sma_10']) / df['sma_10'] * 100
            
            # === RSI (Relative Strength Index) ===
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # === MACD ===
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # === BOLLINGER BANDS ===
            df['bb_middle'] = df['close'].rolling(window=20, min_periods=1).mean()
            bb_std = df['close'].rolling(window=20, min_periods=1).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # === VOLUME ===
            df['volume_sma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
            df['volume_trend'] = df['tick_volume'].rolling(window=5).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1)
            
            # === VOLATILIDADE ===
            df['volatility'] = df['close'].rolling(window=10, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()
            
            # === MOMENTUM ===
            df['roc'] = df['close'].pct_change(periods=5) * 100
            df['momentum'] = df['close'] / df['close'].shift(10) - 1
            
            # === PRESSÃO DE COMPRA/VENDA ===
            df['body_size'] = abs(df['close'] - df['open']) / df['open']
            df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
            df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']
            
            # Pressão baseada em volume e direção
            df['buying_pressure'] = np.where(df['close'] > df['open'], 
                                           df['tick_volume'] * df['body_size'], 0)
            df['selling_pressure'] = np.where(df['close'] < df['open'], 
                                            df['tick_volume'] * df['body_size'], 0)
            df['net_pressure'] = (df['buying_pressure'] - df['selling_pressure']).rolling(window=5).sum()
            
            # === AGRESSÃO ===
            df['aggression_score'] = df['body_size'] * df['volume_ratio'] * (1 + df['volatility_ratio'])
            
            return df.fillna(0)
            
        except Exception as e:
            logging.error(f"Erro ao preparar features: {e}")
            return None
    
    def advanced_strategy(self, df):
        """Estratégia avançada com múltiplos indicadores"""
        try:
            features = self.prepare_features_advanced(df)
            if features is None or len(features) == 0:
                return None, 0, {}
            
            last_row = features.iloc[-1]
            prev_row = features.iloc[-2] if len(features) > 1 else last_row
            
            # Sistema de pontuação avançado
            score = 50  # Base neutra
            signals = {}
            
            # === ANÁLISE DE TENDÊNCIA (25%) ===
            trend_score = 0
            
            # SMA Trend
            if last_row['trend_sma'] > 0:
                trend_score += min(8, last_row['trend_strength'])
                signals['sma_trend'] = f"Alta ({last_row['trend_strength']:.2f}%)"
            else:
                trend_score -= min(8, last_row['trend_strength'])
                signals['sma_trend'] = f"Baixa ({last_row['trend_strength']:.2f}%)"
            
            # EMA Trend
            if last_row['trend_ema'] > 0:
                trend_score += 4
                signals['ema_trend'] = "Alta"
            else:
                trend_score -= 4
                signals['ema_trend'] = "Baixa"
            
            # MACD
            if last_row['macd'] > last_row['macd_signal']:
                trend_score += 3
                signals['macd'] = f"Bullish ({last_row['macd']:.5f})"
            else:
                trend_score -= 3
                signals['macd'] = f"Bearish ({last_row['macd']:.5f})"
            
            score += trend_score
            
            # === ANÁLISE DE MOMENTUM (20%) ===
            momentum_score = 0
            
            # RSI
            if 30 <= last_row['rsi'] <= 40:  # Oversold recovery
                momentum_score += 6
                signals['rsi'] = f"Oversold Recovery ({last_row['rsi']:.1f})"
            elif 60 <= last_row['rsi'] <= 70:  # Overbought entry
                momentum_score -= 6
                signals['rsi'] = f"Overbought ({last_row['rsi']:.1f})"
            else:
                signals['rsi'] = f"Neutral ({last_row['rsi']:.1f})"
            
            # ROC
            if last_row['roc'] > 0.1:
                momentum_score += min(4, last_row['roc'])
                signals['roc'] = f"Positive ({last_row['roc']:.2f}%)"
            elif last_row['roc'] < -0.1:
                momentum_score -= min(4, abs(last_row['roc']))
                signals['roc'] = f"Negative ({last_row['roc']:.2f}%)"
            
            score += momentum_score
            
            # === ANÁLISE DE VOLUME (20%) ===
            volume_score = 0
            
            if last_row['volume_ratio'] > 1.5:
                volume_score += 8
                signals['volume'] = f"Alto ({last_row['volume_ratio']:.2f}x)"
            elif last_row['volume_ratio'] < 0.7:
                volume_score -= 4
                signals['volume'] = f"Baixo ({last_row['volume_ratio']:.2f}x)"
            else:
                signals['volume'] = f"Normal ({last_row['volume_ratio']:.2f}x)"
            
            # Volume trend
            if last_row['volume_trend'] > 0:
                volume_score += 2
                signals['volume_trend'] = "Crescente"
            else:
                volume_score -= 2
                signals['volume_trend'] = "Decrescente"
            
            score += volume_score
            
            # === ANÁLISE DE PRESSÃO (20%) ===
            pressure_score = 0
            
            if last_row['net_pressure'] > 1000:
                pressure_score += min(8, last_row['net_pressure'] / 1000)
                signals['pressure'] = f"Compradora ({last_row['net_pressure']:.0f})"
            elif last_row['net_pressure'] < -1000:
                pressure_score -= min(8, abs(last_row['net_pressure']) / 1000)
                signals['pressure'] = f"Vendedora ({last_row['net_pressure']:.0f})"
            else:
                signals['pressure'] = f"Equilibrada ({last_row['net_pressure']:.0f})"
            
            # Agressão
            if last_row['aggression_score'] > 0.02:
                pressure_score += min(4, last_row['aggression_score'] * 100)
                signals['aggression'] = f"Alta ({last_row['aggression_score']:.3f})"
            else:
                signals['aggression'] = f"Baixa ({last_row['aggression_score']:.3f})"
            
            score += pressure_score
            
            # === ANÁLISE DE VOLATILIDADE (15%) ===
            volatility_score = 0
            
            if 0.8 <= last_row['volatility_ratio'] <= 1.5:  # Volatilidade ideal
                volatility_score += 4
                signals['volatility'] = f"Ideal ({last_row['volatility_ratio']:.2f}x)"
            elif last_row['volatility_ratio'] > 2:  # Muito volátil
                volatility_score -= 3
                signals['volatility'] = f"Muito Alta ({last_row['volatility_ratio']:.2f}x)"
            else:
                signals['volatility'] = f"Normal ({last_row['volatility_ratio']:.2f}x)"
            
            # Bollinger Bands
            if last_row['bb_position'] < 0.2:  # Próximo da banda inferior
                volatility_score += 3
                signals['bollinger'] = f"Oversold ({last_row['bb_position']:.2f})"
            elif last_row['bb_position'] > 0.8:  # Próximo da banda superior
                volatility_score -= 3
                signals['bollinger'] = f"Overbought ({last_row['bb_position']:.2f})"
            else:
                signals['bollinger'] = f"Meio ({last_row['bb_position']:.2f})"
            
            score += volatility_score
            
            # === DETERMINAÇÃO FINAL ===
            # Ajustar thresholds baseado na qualidade dos sinais
            confidence_threshold = 53 if len([s for s in signals.values() if 'Alta' in str(s) or 'Positive' in str(s)]) >= 3 else 55
            
            if score > confidence_threshold:
                direction = '🟩COMPRA'
            elif score < (100 - confidence_threshold):
                direction = '🟥VENDA'
            else:
                direction = None
            
            return direction, score, signals
            
        except Exception as e:
            logging.error(f"Erro na estratégia avançada: {e}")
            return None, 0, {}
    
    def test_indicators_realtime(self, duration_minutes=5):
        """Testa indicadores com dados em tempo real"""
        print(f"🧪 TESTE DE INDICADORES EM TEMPO REAL ({duration_minutes} minutos)")
        print("=" * 60)
        
        start_time = time.time()
        test_results = []
        
        while (time.time() - start_time) < (duration_minutes * 60):
            try:
                # Obter dados em tempo real
                df = self.data_provider.get_historical_data(count=30)
                if df is None or len(df) < 20:
                    print("⚠️ Dados insuficientes, aguardando...")
                    time.sleep(5)
                    continue
                
                # Executar estratégia
                direction, score, signals = self.advanced_strategy(df)
                
                current_price = self.data_provider.get_current_price()
                timestamp = datetime.now().strftime('%H:%M:%S')
                
                # Registrar resultado
                result = {
                    'timestamp': timestamp,
                    'price': current_price,
                    'direction': direction,
                    'score': score,
                    'signals': signals
                }
                test_results.append(result)
                
                # Exibir resultado
                print(f"\n⏰ {timestamp} | 💰 Preço: {current_price:.5f}")
                print(f"🎯 Sinal: {direction or 'NEUTRO'} | 📊 Score: {score:.2f}")
                
                # Mostrar indicadores principais
                key_signals = ['sma_trend', 'rsi', 'volume', 'pressure', 'volatility']
                for key in key_signals:
                    if key in signals:
                        print(f"   {key.upper()}: {signals[key]}")
                
                # Aguardar próxima análise
                time.sleep(10)  # Análise a cada 10 segundos
                
            except Exception as e:
                print(f"❌ Erro no teste: {e}")
                time.sleep(5)
        
        # Análise dos resultados
        self._analyze_test_results(test_results)
        return test_results
    
    def _analyze_test_results(self, results):
        """Analisa resultados do teste"""
        print(f"\n📈 ANÁLISE DOS RESULTADOS")
        print("=" * 40)
        
        if not results:
            print("❌ Nenhum resultado para analisar")
            return
        
        # Estatísticas básicas
        total_tests = len(results)
        buy_signals = len([r for r in results if r['direction'] == '🟩COMPRA'])
        sell_signals = len([r for r in results if r['direction'] == '🟥VENDA'])
        neutral_signals = total_tests - buy_signals - sell_signals
        
        scores = [r['score'] for r in results]
        prices = [r['price'] for r in results if r['price']]
        
        print(f"📊 Total de análises: {total_tests}")
        print(f"🟩 Sinais de COMPRA: {buy_signals} ({buy_signals/total_tests*100:.1f}%)")
        print(f"🟥 Sinais de VENDA: {sell_signals} ({sell_signals/total_tests*100:.1f}%)")
        print(f"⚪ Sinais NEUTROS: {neutral_signals} ({neutral_signals/total_tests*100:.1f}%)")
        
        if scores:
            print(f"\n📈 Score médio: {np.mean(scores):.2f}")
            print(f"📊 Score min/max: {min(scores):.2f} / {max(scores):.2f}")
        
        if len(prices) > 1:
            price_change = ((prices[-1] - prices[0]) / prices[0]) * 100
            price_volatility = np.std(prices) / np.mean(prices) * 100
            print(f"\n💰 Variação de preço: {price_change:+.3f}%")
            print(f"📊 Volatilidade: {price_volatility:.3f}%")
        
        # Análise de indicadores mais ativos
        all_signals = {}
        for result in results:
            for key, value in result['signals'].items():
                if key not in all_signals:
                    all_signals[key] = []
                all_signals[key].append(str(value))
        
        print(f"\n🔍 INDICADORES MAIS VARIÁVEIS:")
        for indicator, values in all_signals.items():
            unique_values = len(set(values))
            if unique_values > 1:
                print(f"   {indicator.upper()}: {unique_values} valores diferentes")

def main():
    """Função principal de teste"""
    tester = StrategyTester()
    
    print("🚀 Iniciando teste da estratégia com dados em tempo real...")
    
    # Aguardar conexão
    if not tester.data_provider.test_connection():
        print("❌ Falha na conexão com dados em tempo real")
        return
    
    print("✅ Conectado aos dados em tempo real!")
    
    # Executar teste
    results = tester.test_indicators_realtime(duration_minutes=3)  # Teste de 3 minutos
    
    print(f"\n🎉 Teste concluído! {len(results)} análises realizadas.")
    
    # Cleanup
    tester.data_provider.shutdown()

if __name__ == "__main__":
    main()
