#!/usr/bin/env python3
"""Script de teste pos-migracao - versao simples"""

import sys
from pathlib import Path

def test_imports():
    """Testa imports necessarios"""
    try:
        from binomo_data_provider import BinomoDataProvider
        print("OK - BinomoDataProvider importado")
        return True
    except ImportError as e:
        print(f"ERRO - Erro de import: {e}")
        return False

def test_config():
    """Testa configuracao"""
    try:
        import json
        with open('config_binomo.json', 'r') as f:
            config = json.load(f)
        print("OK - Configuracao carregada")
        return True
    except Exception as e:
        print(f"ERRO - Erro na configuracao: {e}")
        return False

def test_api_connection():
    """Testa conexao com API"""
    try:
        import json
        from binomo_data_provider import BinomoDataProvider
        
        with open('config_binomo.json', 'r') as f:
            config = json.load(f)
        
        provider = BinomoDataProvider(config)
        if provider.test_connection():
            print("OK - Conexao com API funcionando")
            return True
        else:
            print("ERRO - Falha na conexao com API")
            return False
    except Exception as e:
        print(f"ERRO - Erro no teste de API: {e}")
        return False

def test_data_retrieval():
    """Testa obtencao de dados"""
    try:
        import json
        from binomo_data_provider import BinomoDataProvider
        
        with open('config_binomo.json', 'r') as f:
            config = json.load(f)
        
        provider = BinomoDataProvider(config)
        df = provider.get_historical_data(timeframe="1", count=5)
        
        if df is not None and len(df) > 0:
            print(f"OK - Obtidos {len(df)} candles de dados")
            print(f"     Colunas: {list(df.columns)}")
            return True
        else:
            print("ERRO - Nenhum dado obtido")
            return False
    except Exception as e:
        print(f"ERRO - Erro ao obter dados: {e}")
        return False

if __name__ == "__main__":
    print("TESTE POS-MIGRACAO")
    print("=" * 30)
    
    tests = [
        ("Imports", test_imports),
        ("Configuracao", test_config), 
        ("API Connection", test_api_connection),
        ("Data Retrieval", test_data_retrieval)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\nTestando {name}...")
        result = test_func()
        results.append((name, result))
    
    print(f"\nRESULTADOS:")
    print("=" * 20)
    all_passed = True
    for name, passed in results:
        status = "PASSOU" if passed else "FALHOU"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\nTODOS OS TESTES PASSARAM!")
        print("Proximos passos:")
        print("1. Revisar bot.binomo_new.py")
        print("2. Fazer ajustes manuais necessarios")
        print("3. Testar bot completo")
    else:
        print(f"\nALGUNS TESTES FALHARAM")
        print("Verifique os erros acima antes de prosseguir")
