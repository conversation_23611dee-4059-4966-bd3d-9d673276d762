#!/usr/bin/env python3
"""
Bot Telegram para Debug - Versão Mínima
"""

import telebot
from telebot import types
import os
from dotenv import load_dotenv
import logging

# Carregar variáveis de ambiente
load_dotenv()

# Configuração de logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Token do bot
TOKEN = os.getenv('TELEGRAM_TOKEN')
CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

if not TOKEN:
    print("❌ TELEGRAM_TOKEN não encontrado no .env")
    exit(1)

print(f"✅ Token: {TOKEN[:10]}...")
print(f"✅ Chat ID: {CHAT_ID}")

bot = telebot.TeleBot(TOKEN)

@bot.message_handler(commands=['start'])
def start_handler(message):
    print(f"📨 Comando /start de {message.from_user.id}")
    
    # Criar teclado inline
    keyboard = types.InlineKeyboardMarkup()
    btn1 = types.InlineKeyboardButton("🔴 Botão 1", callback_data="btn1")
    btn2 = types.InlineKeyboardButton("🟢 Botão 2", callback_data="btn2")
    keyboard.add(btn1, btn2)
    
    text = "🤖 Bot Debug\n\nClique nos botões:"
    
    try:
        bot.send_message(message.chat.id, text, reply_markup=keyboard)
        print("✅ Mensagem enviada com botões")
    except Exception as e:
        print(f"❌ Erro ao enviar mensagem: {e}")

@bot.callback_query_handler(func=lambda call: True)
def callback_handler(call):
    print(f"🔔 CALLBACK RECEBIDO!")
    print(f"   Data: {call.data}")
    print(f"   User: {call.from_user.id}")
    print(f"   Chat: {call.message.chat.id}")
    
    try:
        if call.data == "btn1":
            response = "✅ Botão 1 funcionou!"
        elif call.data == "btn2":
            response = "✅ Botão 2 funcionou!"
        else:
            response = f"❓ Callback desconhecido: {call.data}"
        
        # Editar mensagem
        bot.edit_message_text(
            response,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=types.InlineKeyboardMarkup().add(
                types.InlineKeyboardButton("🔄 Voltar", callback_data="start")
            )
        )
        
        # Responder callback
        bot.answer_callback_query(call.id, "Processado!")
        print("✅ Callback processado com sucesso")
        
    except Exception as e:
        print(f"❌ Erro no callback: {e}")
        try:
            bot.answer_callback_query(call.id, f"Erro: {str(e)[:50]}")
        except:
            pass

@bot.message_handler(func=lambda message: True)
def echo_handler(message):
    print(f"📝 Mensagem: '{message.text}' de {message.from_user.id}")
    bot.reply_to(message, f"Você disse: {message.text}")

if __name__ == "__main__":
    print("🚀 Iniciando bot debug...")
    
    # Enviar mensagem de teste se CHAT_ID estiver configurado
    if CHAT_ID:
        try:
            bot.send_message(CHAT_ID, "🔧 Bot Debug iniciado!\n\nUse /start para testar botões")
            print("✅ Mensagem de inicialização enviada")
        except Exception as e:
            print(f"❌ Erro ao enviar mensagem inicial: {e}")
    
    print("📡 Bot rodando... Pressione Ctrl+C para parar")
    
    try:
        bot.polling(none_stop=True, interval=1, timeout=20)
    except KeyboardInterrupt:
        print("\n🛑 Bot parado pelo usuário")
    except Exception as e:
        print(f"❌ Erro no polling: {e}")
