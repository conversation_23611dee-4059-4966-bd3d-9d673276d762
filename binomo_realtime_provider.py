#!/usr/bin/env python3
"""
BinomoRealtimeProvider - Provedor de dados em tempo real usando API oficial Binomo
Substitui a API REST por WebSocket para dados dinâmicos reais
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import time
import logging
from typing import Optional, Dict, List, Callable
import json
import threading
from collections import deque

logger = logging.getLogger(__name__)

class BinomoRealtimeProvider:
    """
    Provedor de dados em tempo real para API Binomo oficial
    Usa WebSocket para dados dinâmicos e reais
    """
    
    def __init__(self, config: dict):
        """
        Inicializa o provedor de dados em tempo real
        
        Args:
            config: Dicionário com configurações da API
        """
        self.config = config
        self.symbol = config.get('SYMBOL', 'Z-CRY/IDX')
        
        # Buffer para armazenar candles em tempo real
        self.candles_buffer = deque(maxlen=1000)  # Últimos 1000 candles
        self.current_price = None
        self.last_update = None
        
        # Threading para WebSocket
        self.websocket_thread = None
        self.is_connected = False
        self.stop_streaming = False
        
        # API Binomo (será inicializada quando necessário)
        self.binomo_api = None
        
        logger.info(f"BinomoRealtimeProvider initialized for symbol: {self.symbol}")
    
    def _init_binomo_api(self):
        """Inicializa a API Binomo oficial"""
        try:
            # Tentar importar a biblioteca oficial
            from binomoapi.stable_api import Binomo
            
            # SSID seria obtido do config ou .env
            # Por enquanto, usar dados simulados se não tiver SSID
            ssid = self.config.get('BINOMO_SSID', None)
            
            if ssid:
                self.binomo_api = Binomo(set_ssid=ssid)
                check_connect, message = self.binomo_api.connect()
                if check_connect:
                    logger.info("Conectado à API oficial Binomo")
                    return True
                else:
                    logger.error(f"Falha na conexão Binomo: {message}")
                    return False
            else:
                logger.warning("SSID não configurado - usando dados simulados")
                return False
                
        except ImportError:
            logger.warning("Biblioteca binomoapi não instalada - usando dados simulados")
            return False
        except Exception as e:
            logger.error(f"Erro ao inicializar API Binomo: {e}")
            return False
    
    def _candle_callback(self, wss, raw_message):
        """Callback para processar candles em tempo real"""
        try:
            # Processar mensagem do WebSocket
            if isinstance(raw_message, str):
                data = json.loads(raw_message)
            else:
                data = raw_message
            
            # Extrair dados do candle
            if 'data' in data and data['data']:
                candle_data = data['data']
                
                # Converter para formato padrão
                candle = {
                    'time': datetime.now(timezone.utc),
                    'open': float(candle_data.get('open', 0)),
                    'high': float(candle_data.get('high', 0)),
                    'low': float(candle_data.get('low', 0)),
                    'close': float(candle_data.get('close', 0)),
                    'tick_volume': int(candle_data.get('volume', 1000)),
                    'spread': 0,
                    'real_volume': int(candle_data.get('volume', 1000))
                }
                
                # Adicionar ao buffer
                self.candles_buffer.append(candle)
                self.current_price = candle['close']
                self.last_update = datetime.now()
                
                logger.debug(f"Candle recebido: {candle['close']:.5f}")
                
        except Exception as e:
            logger.error(f"Erro ao processar candle: {e}")
    
    def start_realtime_stream(self):
        """Inicia stream de dados em tempo real"""
        if self.is_connected:
            return True
        
        # Tentar usar API oficial
        if self._init_binomo_api() and self.binomo_api:
            try:
                # Iniciar stream de candles
                self.binomo_api.start_candles_stream(self.symbol, self._candle_callback)
                self.is_connected = True
                logger.info("Stream de dados em tempo real iniciado")
                return True
            except Exception as e:
                logger.error(f"Erro ao iniciar stream: {e}")
        
        # Fallback: usar dados simulados em tempo real
        self._start_simulated_stream()
        return True
    
    def _start_simulated_stream(self):
        """Inicia stream simulado para testes"""
        logger.info("Iniciando stream simulado de dados")
        
        def simulate_candles():
            base_price = 641.867
            
            while not self.stop_streaming:
                try:
                    # Simular variação de preço realística
                    price_change = np.random.normal(0, 0.002)  # ±0.2% variação
                    new_price = base_price * (1 + price_change)
                    
                    # Simular OHLC
                    high_var = abs(np.random.normal(0, 0.001))
                    low_var = abs(np.random.normal(0, 0.001))
                    
                    candle = {
                        'time': datetime.now(timezone.utc),
                        'open': base_price,
                        'high': new_price + high_var,
                        'low': new_price - low_var,
                        'close': new_price,
                        'tick_volume': int(np.random.normal(1000, 300)),
                        'spread': 0,
                        'real_volume': int(np.random.normal(1000, 300))
                    }
                    
                    # Adicionar ao buffer
                    self.candles_buffer.append(candle)
                    self.current_price = new_price
                    self.last_update = datetime.now()
                    base_price = new_price  # Atualizar base
                    
                    # Aguardar 1 segundo (simular candles de 1 segundo)
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Erro na simulação: {e}")
                    time.sleep(1)
        
        # Iniciar thread de simulação
        self.websocket_thread = threading.Thread(target=simulate_candles, daemon=True)
        self.websocket_thread.start()
        self.is_connected = True
    
    def get_historical_data(self, timeframe: str = "1", count: int = 20, 
                          end_time: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Obtém dados históricos do buffer em tempo real
        
        Args:
            timeframe: Timeframe em minutos
            count: Número de candles desejados
            end_time: Não usado (sempre dados mais recentes)
            
        Returns:
            DataFrame com dados OHLC
        """
        try:
            # Garantir que o stream está ativo
            if not self.is_connected:
                self.start_realtime_stream()
                time.sleep(2)  # Aguardar alguns candles
            
            # Verificar se temos dados suficientes
            if len(self.candles_buffer) < count:
                logger.warning(f"Buffer tem apenas {len(self.candles_buffer)} candles, solicitado {count}")
                # Aguardar mais dados
                time.sleep(1)
            
            # Converter buffer para DataFrame
            if len(self.candles_buffer) == 0:
                return None
            
            # Pegar os últimos N candles
            recent_candles = list(self.candles_buffer)[-count:]
            df = pd.DataFrame(recent_candles)
            
            # Garantir tipos corretos
            numeric_columns = ['open', 'high', 'low', 'close', 'tick_volume', 'real_volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            logger.info(f"Retornados {len(df)} candles em tempo real")
            return df
            
        except Exception as e:
            logger.error(f"Erro ao obter dados históricos: {e}")
            return None
    
    def get_current_price(self) -> Optional[float]:
        """Obtém preço atual em tempo real"""
        try:
            if not self.is_connected:
                self.start_realtime_stream()
                time.sleep(1)
            
            if self.current_price is not None:
                return float(self.current_price)
            
            # Fallback: usar último candle do buffer
            if len(self.candles_buffer) > 0:
                return float(self.candles_buffer[-1]['close'])
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter preço atual: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Testa conectividade"""
        try:
            if not self.is_connected:
                self.start_realtime_stream()
                time.sleep(2)
            
            # Verificar se temos dados recentes
            if self.last_update:
                time_diff = (datetime.now() - self.last_update).total_seconds()
                return time_diff < 10  # Dados de menos de 10 segundos
            
            return len(self.candles_buffer) > 0
            
        except Exception as e:
            logger.error(f"Erro no teste de conexão: {e}")
            return False
    
    def shutdown(self):
        """Finaliza o provedor"""
        self.stop_streaming = True
        self.is_connected = False
        
        if self.binomo_api:
            try:
                # Parar streams se possível
                pass
            except:
                pass
        
        logger.info("BinomoRealtimeProvider finalizado")

# Exemplo de uso
if __name__ == "__main__":
    config = {
        'SYMBOL': 'Z-CRY/IDX',
        'BINOMO_SSID': None  # Configurar se tiver
    }
    
    provider = BinomoRealtimeProvider(config)
    
    if provider.test_connection():
        print("✅ Conexão OK")
        
        # Testar dados em tempo real
        for i in range(5):
            price = provider.get_current_price()
            df = provider.get_historical_data(count=5)
            
            print(f"Teste {i+1}: Preço={price:.5f}, Candles={len(df) if df is not None else 0}")
            time.sleep(3)
    else:
        print("❌ Falha na conexão")
    
    provider.shutdown()
