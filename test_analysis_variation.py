#!/usr/bin/env python3
"""Teste para verificar variação na análise técnica"""

import time
import json
from binomo_data_provider import BinomoDataProvider
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

def prepare_features(df):
    """Prepara features para análise técnica (versão simplificada)"""
    if len(df) < 10:
        return None
    
    # Calcular indicadores básicos
    df['sma_5'] = df['close'].rolling(window=5).mean()
    df['sma_10'] = df['close'].rolling(window=10).mean()
    df['roc'] = df['close'].pct_change(periods=5) * 100
    df['volume_ratio'] = df['tick_volume'] / df['tick_volume'].rolling(window=10).mean()
    df['volatility'] = df['close'].rolling(window=5).std()
    df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=10).mean()
    
    # Calcular agressão (body size)
    df['body_size'] = abs(df['close'] - df['open']) / df['open']
    df['aggression_score'] = df['body_size'] * df['volume_ratio']
    
    # Tendência
    df['trend'] = (df['sma_5'] - df['sma_10']) / df['sma_10'] * 100
    df['trend_strength'] = abs(df['trend'])
    
    return df.dropna()

def test_analysis_variation():
    """Testa se a análise técnica está variando"""
    print("🔍 TESTE DE VARIAÇÃO DA ANÁLISE TÉCNICA")
    print("=" * 50)
    
    # Carregar configuração
    with open('config_binomo.json', 'r') as f:
        config = json.load(f)
    
    provider = BinomoDataProvider(config)
    results = []
    
    print("Coletando dados em 5 momentos diferentes...")
    
    for i in range(5):
        print(f"\n📊 Coleta {i+1}/5:")
        
        # Obter dados
        df = provider.get_historical_data(timeframe="1", count=20)
        if df is None or len(df) == 0:
            print("   ❌ Erro ao obter dados")
            continue
        
        # Preparar features
        features = prepare_features(df)
        if features is None or len(features) == 0:
            print("   ❌ Erro ao preparar features")
            continue
        
        last_row = features.iloc[-1]
        
        # Extrair métricas
        metrics = {
            'timestamp': df.iloc[-1]['time'] if 'time' in df.columns else 'N/A',
            'close_price': float(last_row['close']),
            'volume_ratio': float(last_row['volume_ratio']),
            'aggression_score': float(last_row['aggression_score']),
            'trend_strength': float(last_row['trend_strength']),
            'volatility_ratio': float(last_row['volatility_ratio'])
        }
        
        results.append(metrics)
        
        print(f"   💰 Preço: {metrics['close_price']:.5f}")
        print(f"   📊 Volume: {metrics['volume_ratio']:.2f}x")
        print(f"   ⚡ Agressão: {metrics['aggression_score']:.3f}")
        print(f"   📈 Tendência: {metrics['trend_strength']:.1f}%")
        print(f"   📊 Volatilidade: {metrics['volatility_ratio']:.2f}x")
        
        # Aguardar 20 segundos para cache expirar
        if i < 4:
            print("   ⏳ Aguardando 20s para próxima coleta...")
            time.sleep(20)
    
    # Analisar variação
    print(f"\n📈 ANÁLISE DE VARIAÇÃO:")
    print("=" * 30)
    
    if len(results) < 2:
        print("❌ Dados insuficientes para análise")
        return
    
    # Verificar se há variação nos valores
    metrics_to_check = ['close_price', 'volume_ratio', 'aggression_score', 'trend_strength', 'volatility_ratio']
    
    variations = {}
    for metric in metrics_to_check:
        values = [r[metric] for r in results]
        min_val = min(values)
        max_val = max(values)
        variation = abs(max_val - min_val) / min_val * 100 if min_val != 0 else 0
        variations[metric] = variation
        
        status = "✅ VARIANDO" if variation > 0.1 else "❌ ESTÁTICO"
        print(f"{metric:15}: {status} ({variation:.2f}% variação)")
    
    # Resultado final
    varying_count = sum(1 for v in variations.values() if v > 0.1)
    total_metrics = len(variations)
    
    print(f"\n🎯 RESULTADO FINAL:")
    print(f"Métricas variando: {varying_count}/{total_metrics}")
    
    if varying_count >= total_metrics * 0.6:  # 60% ou mais variando
        print("✅ ANÁLISE TÉCNICA ESTÁ DINÂMICA!")
    else:
        print("❌ ANÁLISE TÉCNICA AINDA ESTÁTICA")
        print("💡 Sugestão: Verificar timestamps da API ou implementar mais variação")

if __name__ == "__main__":
    test_analysis_variation()
