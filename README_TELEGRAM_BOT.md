# 🤖 Bot Telegram - Crypto IDX Trading

Bot completo para controle do sistema de trading via Telegram com interface de botões interativos.

## 🚀 Funcionalidades

### 📊 **Análise e Sinais**
- **🎯 Gerar Sinal**: Análise única com sinal imediato
- **📊 Análise Única**: Análise completa com múltiplos sinais
- **🔴 Tempo Real**: Trading automático contínuo
- **📈 Backtest**: Teste da estratégia em dados históricos

### 📈 **Monitoramento**
- **✅ Verificar Resultados**: Atualiza sinais pendentes
- **📊 Estatísticas**: Relatório completo de performance
- **📡 Status**: Estado atual do bot e conexões
- **🔄 Relatórios Periódicos**: Análises automáticas

### ⚙️ **Configurações**
- **🔔 Auto Sinais**: Liga/desliga envio automático
- **📊 Auto Análise**: Liga/desliga relatórios periódicos
- **⏱️ Timeframe**: Configurar período de análise
- **🎯 Confiança Mínima**: Filtro de qualidade dos sinais

## 🛠️ Configuração

### 1. **Arquivo .env**
```env
# Token do Bot Telegram (obrigatório)
TELEGRAM_TOKEN=**********************************************

# ID do Chat Telegram (obrigatório)
TELEGRAM_CHAT_ID=-1002339597969

# Configurações de Trading
TRADING_SYMBOL=CRYIDZbnm
MIN_CONFIDENCE=55.0

# API Binomo
BINOMO_API_BASE=https://api.binomo.com
BINOMO_SYMBOL=Z-CRY%2FIDX
BINOMO_TIMEFRAME=1
BINOMO_LOCALE=br
```

### 2. **Dependências**
```bash
pip install pyTelegramBotAPI pytz scikit-learn pandas numpy
```

### 3. **Executar Bot**
```bash
python telegram_bot.py
```

## 📱 Como Usar

### **Iniciar Bot**
1. Envie `/start` no chat do Telegram
2. Use os botões interativos para navegar
3. Todas as funções são acessíveis via botões

### **Menu Principal**
```
🎯 Gerar Sinal     📊 Análise Única
🔴 Iniciar Tempo Real   📈 Backtest
✅ Verificar Resultados  📊 Estatísticas
⚙️ Configurações    📡 Status
🛑 Parar Tudo
```

### **Modo Tempo Real**
- Análise contínua a cada 60 segundos
- Envio automático de sinais (se habilitado)
- Relatórios periódicos a cada 5 ciclos
- Controle total via botões

## 🎯 Tipos de Sinais

### **Exemplo de Sinal**
```
🚨 SINAL EM TEMPO REAL

📈 CALL 📈
⏰ 21:15 (UTC) | 18:15 (SP)
💰 Preço: 641.86702
🎯 Confiança: ⭐ ALTA
📊 Score: 61.0/100

✅ Top Confirmações:
1. Trend Consensus: Forte Alta (3/3)
2. Rsi: Overbought (66.9)
3. Volatility: Ideal (1.67x)
4. Bollinger: Meio (0.85)

⏱️ Expiração: 1 minuto
🎯 Símbolo: Z-CRY%2FIDX
```

### **Níveis de Confiança**
- 🔥 **MUITO_ALTA**: 3+ fatores de alta confiança
- ⭐ **ALTA**: 2+ fatores de confiança
- 📊 **MÉDIA**: 1+ fator de confiança
- ⚠️ **BAIXA**: Filtrado automaticamente

## 📊 Estatísticas

### **Relatório Completo**
```
📊 ESTATÍSTICAS COMPLETAS

🎯 Geral:
• Total: 364 sinais
• Wins: 187 (51.37%)
• Losses: 177
• Lucro: -18.05
• Média: -0.0496

📈 Por Direção:
• CALL: 187/364 (51.37%)

🏆 Top Confirmações:
1. RSI Sobrecomprado: 83.33%
2. Engolfo de Baixa: 75.0%
3. Shooting Star: 66.67%
```

## 🔧 Configurações Avançadas

### **Auto Sinais**
- **ON**: Envia sinais automaticamente no tempo real
- **OFF**: Apenas análise, sem envio

### **Auto Análise**
- **ON**: Relatórios periódicos a cada 5 ciclos
- **OFF**: Apenas sinais, sem relatórios

### **Filtros**
- **Confiança Mínima**: Score mínimo para envio
- **Horário de Trading**: Evita madrugada (2h-6h)
- **Consenso**: Mínimo 60% de acordo entre indicadores

## 🚨 Comandos de Emergência

### **Parar Tudo**
- Para todas as operações ativas
- Finaliza workers em background
- Salva todos os dados

### **Status do Bot**
- Verifica conexão com API
- Estado de todas as operações
- Integridade dos arquivos

## 📁 Arquivos Gerados

- `results/signals_history.json`: Histórico completo
- `results/strategy_analysis.json`: Análises estatísticas
- Logs automáticos de todas as operações

## ⚠️ Importante

1. **Configurar .env corretamente**
2. **Verificar conexão com API Binomo**
3. **Monitorar resultados regularmente**
4. **Usar filtros de confiança adequados**
5. **Backtest antes de usar em produção**

## 🎯 Estratégia Otimizada v2.0

- **Multi-indicador**: 15+ indicadores técnicos
- **Sistema adaptativo**: Score dinâmico 50-100
- **Consenso inteligente**: Validação cruzada
- **Fuso horário SP**: Horários brasileiros
- **API real-time**: Dados Binomo em tempo real

---

**🚀 Bot 100% funcional e pronto para uso!**
