#!/usr/bin/env python3
"""
Bot Telegram Funcional para Crypto IDX Trading
Baseado nos padrões dos bots existentes na pasta
"""

import telebot
from telebot import types
import os
from dotenv import load_dotenv
import logging
import time
from datetime import datetime

# Importar funções do bot principal
from crypto_idx_bot import (
    obter_dados_realtime, gerar_sinais, executar_analise_unica,
    verificar_resultados, gerar_analise_estrategia,
    get_current_time_sp
)

# Carregar variáveis de ambiente
load_dotenv()

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CryptoTradingBot:
    def __init__(self):
        self.token = os.getenv('TELEGRAM_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        if not self.token:
            raise ValueError("TELEGRAM_TOKEN deve estar configurado no .env")
        
        self.bot = telebot.TeleBot(self.token)
        self._setup_handlers()
        
        # Configurações
        self.config = {
            'symbol': os.getenv('BINOMO_SYMBOL', 'Z-CRY%2FIDX'),
            'timeframe': os.getenv('BINOMO_TIMEFRAME', '1'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
        }
        
        logger.info("🚀 Bot Telegram inicializado com sucesso!")

    def _setup_handlers(self):
        """Configura os handlers do bot"""
        
        @self.bot.message_handler(commands=['start'])
        def start_handler(message):
            logger.info(f"Comando /start de {message.from_user.id}")
            
            # Criar teclado inline
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # Botões principais
            btn1 = types.InlineKeyboardButton("🎯 Gerar Sinal", callback_data="signal")
            btn2 = types.InlineKeyboardButton("📊 Análise", callback_data="analysis")
            btn3 = types.InlineKeyboardButton("📈 Estatísticas", callback_data="stats")
            btn4 = types.InlineKeyboardButton("📡 Status", callback_data="status")
            btn5 = types.InlineKeyboardButton("✅ Verificar Resultados", callback_data="results")
            btn6 = types.InlineKeyboardButton("🔄 Menu", callback_data="menu")
            
            keyboard.add(btn1, btn2)
            keyboard.add(btn3, btn4)
            keyboard.add(btn5)
            keyboard.add(btn6)
            
            welcome_text = (
                "🚀 **BOT CRYPTO IDX - BINOMO**\n\n"
                "🎯 **Funcionalidades:**\n"
                "• Geração de sinais em tempo real\n"
                "• Análise técnica avançada\n"
                "• Estatísticas de performance\n"
                "• Verificação de resultados\n\n"
                "📊 **Estratégia Otimizada v2.0**\n"
                "• Multi-indicador avançado\n"
                "• Sistema de confiança adaptativo\n"
                "• Fuso horário São Paulo\n\n"
                "Escolha uma opção abaixo:"
            )
            
            try:
                self.bot.send_message(
                    message.chat.id, 
                    welcome_text, 
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                logger.info("✅ Mensagem de boas-vindas enviada")
            except Exception as e:
                logger.error(f"Erro ao enviar mensagem: {e}")
                # Fallback sem markdown
                self.bot.send_message(
                    message.chat.id, 
                    "BOT CRYPTO IDX - BINOMO\n\nEscolha uma opcao:", 
                    reply_markup=keyboard
                )

        @self.bot.callback_query_handler(func=lambda call: True)
        def callback_handler(call):
            logger.info(f"📞 Callback: '{call.data}' de {call.from_user.id}")
            
            try:
                chat_id = call.message.chat.id
                message_id = call.message.message_id
                
                if call.data == "signal":
                    self.handle_signal(chat_id, message_id)
                    
                elif call.data == "analysis":
                    self.handle_analysis(chat_id, message_id)
                    
                elif call.data == "stats":
                    self.handle_stats(chat_id, message_id)
                    
                elif call.data == "status":
                    self.handle_status(chat_id, message_id)
                    
                elif call.data == "results":
                    self.handle_results(chat_id, message_id)
                    
                elif call.data == "menu":
                    self.handle_menu(chat_id, message_id)
                    
                else:
                    logger.warning(f"Callback desconhecido: {call.data}")
                
                # Responder ao callback
                self.bot.answer_callback_query(call.id)
                logger.info(f"✅ Callback {call.data} processado")
                
            except Exception as e:
                logger.error(f"❌ Erro no callback: {e}")
                try:
                    self.bot.answer_callback_query(call.id, f"Erro: {str(e)[:50]}")
                except:
                    pass

        @self.bot.message_handler(commands=['sinal'])
        def signal_command(message):
            logger.info(f"Comando /sinal de {message.from_user.id}")
            self.send_signal_text(message.chat.id)

        @self.bot.message_handler(commands=['stats'])
        def stats_command(message):
            logger.info(f"Comando /stats de {message.from_user.id}")
            self.send_stats_text(message.chat.id)

    def create_menu_keyboard(self):
        """Cria teclado do menu"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        btn1 = types.InlineKeyboardButton("🎯 Gerar Sinal", callback_data="signal")
        btn2 = types.InlineKeyboardButton("📊 Análise", callback_data="analysis")
        btn3 = types.InlineKeyboardButton("📈 Estatísticas", callback_data="stats")
        btn4 = types.InlineKeyboardButton("📡 Status", callback_data="status")
        btn5 = types.InlineKeyboardButton("✅ Verificar Resultados", callback_data="results")
        
        keyboard.add(btn1, btn2)
        keyboard.add(btn3, btn4)
        keyboard.add(btn5)
        
        return keyboard

    def handle_signal(self, chat_id, message_id):
        """Gera um sinal"""
        try:
            self.bot.edit_message_text(
                "🔄 Gerando sinal... Aguarde...",
                chat_id, message_id
            )
            
            # Obter dados
            df = obter_dados_realtime(count=100, timeframe=self.config['timeframe'])
            
            if df is None or len(df) == 0:
                self.bot.edit_message_text(
                    "❌ Erro ao obter dados da API\n\n🔄 Tente novamente",
                    chat_id, message_id,
                    reply_markup=self.create_menu_keyboard()
                )
                return
            
            # Gerar sinais
            hora_ultima_vela = df.iloc[-1]['timestamp']
            sinais = gerar_sinais(df, hora_ultima_vela)
            
            if sinais:
                sinal = sinais[0]
                
                confidence_level = sinal.get('confidence_level', 'MEDIA')
                score = sinal.get('score', 0)
                
                signal_text = (
                    f"🎯 **SINAL GERADO**\n\n"
                    f"📈 **Direção:** {sinal['direcao']}\n"
                    f"⏰ **Horário:** {sinal['hora']} (UTC) | {get_current_time_sp().strftime('%H:%M')} (SP)\n"
                    f"🎯 **Confiança:** {confidence_level}\n"
                    f"📊 **Score:** {score:.1f}/100\n\n"
                    f"✅ **Confirmações ({len(sinal['confirmacoes'])}):**\n"
                )
                
                for i, conf in enumerate(sinal['confirmacoes'][:4], 1):
                    signal_text += f"{i}. {conf[:40]}{'...' if len(conf) > 40 else ''}\n"
                
                signal_text += f"\n💰 **Preço:** {df.iloc[-1]['close']:.5f}"
                
                self.bot.edit_message_text(
                    signal_text,
                    chat_id, message_id,
                    reply_markup=self.create_menu_keyboard(),
                    parse_mode='Markdown'
                )
                
            else:
                self.bot.edit_message_text(
                    "⚪ Nenhum sinal encontrado no momento\n\n💡 Tente novamente em alguns minutos",
                    chat_id, message_id,
                    reply_markup=self.create_menu_keyboard()
                )
                
        except Exception as e:
            logger.error(f"Erro ao gerar sinal: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao gerar sinal\n\n{str(e)[:100]}",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def handle_analysis(self, chat_id, message_id):
        """Executa análise completa"""
        try:
            self.bot.edit_message_text(
                "📊 Executando análise completa... Aguarde...",
                chat_id, message_id
            )
            
            sinais = executar_analise_unica(salvar_sinais=True)
            
            if sinais:
                analysis_text = (
                    f"📊 **ANÁLISE COMPLETA**\n\n"
                    f"🎯 **Sinais Encontrados:** {len(sinais)}\n"
                    f"🕐 **Horário:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n\n"
                )
                
                for i, sinal in enumerate(sinais[:3], 1):
                    confidence_level = sinal.get('confidence_level', 'MEDIA')
                    score = sinal.get('score', 0)
                    
                    analysis_text += (
                        f"**Sinal {i}:**\n"
                        f"📈 {sinal['direcao']} às {sinal['hora']}\n"
                        f"🎯 {confidence_level} ({score:.1f}/100)\n"
                        f"✅ {len(sinal['confirmacoes'])} confirmações\n\n"
                    )
                
                if len(sinais) > 3:
                    analysis_text += f"... e mais {len(sinais) - 3} sinais\n\n"
                
                analysis_text += "💾 Sinais salvos para tracking!"
                
            else:
                analysis_text = (
                    "📊 **ANÁLISE COMPLETA**\n\n"
                    "⚪ Nenhum sinal encontrado\n\n"
                    "💡 Condições não atendem aos critérios"
                )
            
            self.bot.edit_message_text(
                analysis_text,
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard(),
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Erro na análise: {e}")
            self.bot.edit_message_text(
                f"❌ Erro na análise\n\n{str(e)[:100]}",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def handle_stats(self, chat_id, message_id):
        """Mostra estatísticas"""
        try:
            self.bot.edit_message_text(
                "📊 Carregando estatísticas...",
                chat_id, message_id
            )

            analise = gerar_analise_estrategia()

            if analise:
                stats = analise['estatisticas_gerais']

                stats_text = (
                    f"📊 **ESTATÍSTICAS**\n\n"
                    f"🎯 **Total:** {stats['total_sinais']} sinais\n"
                    f"✅ **Wins:** {stats['gains']} ({stats['win_rate']}%)\n"
                    f"❌ **Losses:** {stats['losses']}\n"
                    f"💰 **Lucro:** {stats['lucro_total']:+.2f}\n"
                    f"📊 **Média:** {stats['lucro_medio']:+.4f}\n\n"
                    f"🕐 **Atualizado:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)"
                )
            else:
                stats_text = "❌ Nenhuma estatística disponível\n\n💡 Execute algumas análises primeiro"

            self.bot.edit_message_text(
                stats_text,
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar stats: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao carregar estatísticas",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def handle_status(self, chat_id, message_id):
        """Mostra status do bot"""
        try:
            self.bot.edit_message_text(
                "📡 Verificando status...",
                chat_id, message_id
            )

            # Testar conexão
            df = obter_dados_realtime(count=1, timeframe="1")
            api_status = "✅ Online" if df is not None else "❌ Offline"

            status_text = (
                f"📡 **STATUS DO BOT**\n\n"
                f"🤖 **Bot:** ✅ Online\n"
                f"📊 **API Binomo:** {api_status}\n"
                f"🕐 **Horário:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n"
                f"🎯 **Símbolo:** {self.config['symbol']}\n"
                f"⏱️ **Timeframe:** {self.config['timeframe']}m\n"
                f"🎯 **Confiança Mín:** {self.config['min_confidence']}%"
            )

            self.bot.edit_message_text(
                status_text,
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar status: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao verificar status",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def handle_results(self, chat_id, message_id):
        """Verifica resultados pendentes"""
        try:
            self.bot.edit_message_text(
                "🔍 Verificando resultados pendentes...",
                chat_id, message_id
            )

            verificar_resultados()

            self.bot.edit_message_text(
                "✅ **RESULTADOS VERIFICADOS**\n\n"
                "🔄 Sinais pendentes atualizados\n"
                "📊 Use 'Estatísticas' para ver resultados\n"
                "💾 Dados salvos automaticamente",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao verificar resultados: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao verificar resultados",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def handle_menu(self, chat_id, message_id):
        """Volta ao menu principal"""
        try:
            menu_text = (
                f"🎯 **CRYPTO IDX BOT - MENU**\n\n"
                f"🕐 Horário: {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n"
                f"📊 Símbolo: {self.config['symbol']}\n"
                f"⏱️ Timeframe: {self.config['timeframe']}m\n\n"
                "Escolha uma opção:"
            )

            self.bot.edit_message_text(
                menu_text,
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar menu: {e}")
            self.bot.edit_message_text(
                "CRYPTO IDX BOT - MENU\n\nEscolha uma opcao:",
                chat_id, message_id,
                reply_markup=self.create_menu_keyboard()
            )

    def send_signal_text(self, chat_id):
        """Envia sinal via comando de texto"""
        try:
            df = obter_dados_realtime(count=100, timeframe=self.config['timeframe'])

            if df is None:
                self.bot.send_message(chat_id, "❌ Erro ao obter dados da API")
                return

            hora_ultima_vela = df.iloc[-1]['timestamp']
            sinais = gerar_sinais(df, hora_ultima_vela)

            if sinais:
                sinal = sinais[0]
                signal_text = (
                    f"🎯 SINAL: {sinal['direcao']}\n"
                    f"⏰ Horário: {sinal['hora']}\n"
                    f"✅ Confirmações: {len(sinal['confirmacoes'])}\n"
                    f"💰 Preço: {df.iloc[-1]['close']:.5f}"
                )
                self.bot.send_message(chat_id, signal_text)
            else:
                self.bot.send_message(chat_id, "⚪ Nenhum sinal encontrado")

        except Exception as e:
            logger.error(f"Erro ao enviar sinal: {e}")
            self.bot.send_message(chat_id, f"❌ Erro: {str(e)[:100]}")

    def send_stats_text(self, chat_id):
        """Envia estatísticas via comando de texto"""
        try:
            analise = gerar_analise_estrategia()

            if analise:
                stats = analise['estatisticas_gerais']
                stats_text = (
                    f"📊 ESTATÍSTICAS\n"
                    f"Total: {stats['total_sinais']}\n"
                    f"Wins: {stats['gains']} ({stats['win_rate']}%)\n"
                    f"Losses: {stats['losses']}\n"
                    f"Lucro: {stats['lucro_total']:+.2f}"
                )
                self.bot.send_message(chat_id, stats_text)
            else:
                self.bot.send_message(chat_id, "❌ Nenhuma estatística disponível")

        except Exception as e:
            logger.error(f"Erro ao enviar stats: {e}")
            self.bot.send_message(chat_id, f"❌ Erro: {str(e)[:100]}")

    def run(self):
        """Executa o bot"""
        logger.info("🚀 Iniciando Bot Telegram...")
        
        if self.chat_id:
            try:
                startup_msg = (
                    "🚀 **BOT CRYPTO IDX ATIVO!**\n\n"
                    f"🕐 Iniciado: {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n"
                    f"📊 Símbolo: {self.config['symbol']}\n"
                    f"⏱️ Timeframe: {self.config['timeframe']}m\n\n"
                    "Use /start para acessar o menu!"
                )
                
                self.bot.send_message(self.chat_id, startup_msg, parse_mode='Markdown')
                logger.info("✅ Mensagem de inicialização enviada")
                
            except Exception as e:
                logger.error(f"Erro ao enviar mensagem inicial: {e}")
        
        logger.info("📡 Bot rodando... Pressione Ctrl+C para parar")
        self.bot.polling(none_stop=True, interval=1, timeout=20)

if __name__ == "__main__":
    try:
        bot = CryptoTradingBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n🛑 Bot interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        logger.error(f"Erro fatal: {e}")
