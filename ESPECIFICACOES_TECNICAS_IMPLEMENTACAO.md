# Especificações Técnicas para Implementação - Bot Binomo

## 🔧 Detalhes Técnicos da Migração

### 1. BinomoDataProvider - Especificações Detalhadas

#### 1.1 Interface da Classe
```python
class BinomoDataProvider:
    def __init__(self, config: dict)
    def get_historical_data(self, timeframe: str, count: int, end_time: datetime = None) -> pd.DataFrame
    def get_current_price(self) -> float
    def test_connection(self) -> bool
    def get_available_symbols(self) -> list
    def shutdown(self)
```

#### 1.2 Estrutura de Dados Retornada
```python
# DataFrame columns esperadas (compatível com MT5)
columns = ['time', 'open', 'high', 'low', 'close', 'tick_volume', 'spread', 'real_volume']
```

#### 1.3 Sistema de Cache
- **TTL padrão**: 60 segundos para dados M1
- **Chave de cache**: `{symbol}_{timeframe}_{timestamp}`
- **Invalidação**: Automática por TTL + manual por demanda
- **Storage**: Memória (dict) com limite de 1000 entradas

#### 1.4 Rate Limiting
- **Limite**: 10 requests por minuto
- **Backoff**: Exponencial (1s, 2s, 4s, 8s)
- **Max retries**: 3 tentativas
- **Timeout**: 10 segundos por request

### 2. Modificações Específicas no bot.binomo.py

#### 2.1 Imports a Remover
```python
# REMOVER
import MetaTrader5 as mt5

# ADICIONAR
from binomo_data_provider import BinomoDataProvider
import json
```

#### 2.2 Modificações no __init__
```python
def __init__(self):
    self._load_config()
    self.bot = telebot.TeleBot(self.config['telegram_token'])
    self._setup_telegram_handlers()
    self.scaler = StandardScaler()
    self.active_trade = None
    self.processing_signal = False
    
    # NOVO: Inicializar BinomoDataProvider
    self.data_provider = BinomoDataProvider(self.config)
```

#### 2.3 Atualização do _load_config
```python
def _load_config(self):
    """Carrega variáveis do .env e config_binomo.json"""
    load_dotenv()
    
    # Configurações básicas do .env
    self.config = {
        'telegram_token': os.getenv('TELEGRAM_TOKEN'),
        'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
        'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
    }
    
    # NOVO: Carregar configurações da API Binomo
    try:
        with open('config_binomo.json', 'r') as f:
            binomo_config = json.load(f)
            self.config.update(binomo_config)
    except FileNotFoundError:
        # Configurações padrão
        self.config.update({
            'BINOMO_API_BASE': 'https://api.binomo.com',
            'SYMBOL': 'Z-CRY%2FIDX',
            'TIMEFRAME': '1',
            'LOCALE': 'br',
            'REQUEST_TIMEOUT': 10,
            'MAX_RETRIES': 3,
            'CACHE_DURATION': 60
        })
```

#### 2.4 Modificação do predict_next_move
```python
def predict_next_move(self):
    """Faz a previsão do próximo movimento com análise avançada"""
    try:
        # SUBSTITUIR: rates = mt5.copy_rates_from_pos(...)
        # POR:
        df = self.data_provider.get_historical_data(
            timeframe=self.config['TIMEFRAME'], 
            count=20
        )
        
        if df is None or len(df) == 0:
            logging.error("Erro ao obter dados para previsão.")
            return None, 0.0

        features = self.prepare_features(df)
        # ... resto do método permanece igual
```

#### 2.5 Modificação do get_current_price
```python
def get_current_price(self):
    """Obtém o preço atual do ativo."""
    try:
        # SUBSTITUIR: tick = mt5.symbol_info_tick(...)
        # POR:
        return self.data_provider.get_current_price()
    except Exception as e:
        logging.error(f"Erro ao obter preço atual: {e}")
        return None
```

#### 2.6 Modificação do run
```python
def run(self):
    """Executa o bot"""
    # REMOVER: if not mt5.initialize():
    # SUBSTITUIR POR:
    if not self.data_provider.test_connection():
        logging.error("Erro ao conectar com API Binomo")
        return
    
    logging.info("🚀 Bot de trading iniciado com API Binomo!")
    self.bot.polling()
```

### 3. Arquivo config_binomo.json

```json
{
  "BINOMO_API_BASE": "https://api.binomo.com",
  "SYMBOL": "Z-CRY%2FIDX",
  "TIMEFRAME": "1",
  "LOCALE": "br",
  "REQUEST_TIMEOUT": 10,
  "MAX_RETRIES": 3,
  "CACHE_DURATION": 60,
  "RATE_LIMIT_PER_MINUTE": 10,
  "ENABLE_CACHE": true,
  "LOG_LEVEL": "INFO",
  "FALLBACK_ENABLED": false,
  "DATA_VALIDATION": true,
  "RETRY_BACKOFF_FACTOR": 2.0
}
```

### 4. Estrutura de Arquivos Final

```
Api-Binomo/
├── bot.binomo.py                    # Bot principal (modificado)
├── binomo_data_provider.py          # NOVO: Provider de dados
├── config_binomo.json               # NOVO: Configurações
├── requirements.txt                 # Atualizado (sem MT5)
├── .env                            # Mantido
├── test_binomo_integration.py       # NOVO: Testes
├── migration_script.py              # NOVO: Script de migração
├── ANALISE_DETALHADA_BOT_BINOMO.md  # Análise completa
├── ESPECIFICACOES_TECNICAS_IMPLEMENTACAO.md  # Este arquivo
└── API/
    ├── binApiLinkGen.py            # Mantido (melhorado)
    ├── getCan.py                   # Mantido (melhorado)
    ├── test_binomo_api.py          # Mantido
    └── test_timeframes.py          # Mantido
```

### 5. Requirements.txt Atualizado

```txt
# REMOVER: MetaTrader5
telebot==0.0.5
pandas==2.0.3
numpy==1.24.3
scikit-learn==1.3.0
python-dotenv==1.0.0
requests==2.31.0
python-telegram-bot==20.3
```

### 6. Validações e Testes Necessários

#### 6.1 Testes Unitários
- ✅ Teste de conectividade API
- ✅ Teste de cache funcionando
- ✅ Teste de rate limiting
- ✅ Teste de retry logic
- ✅ Teste de conversão de dados
- ✅ Teste de validação de dados

#### 6.2 Testes de Integração
- ✅ Teste de geração de sinais
- ✅ Teste de análise técnica
- ✅ Teste de bot Telegram
- ✅ Teste de performance
- ✅ Teste de fallback

#### 6.3 Testes de Performance
- ✅ Latência < 2 segundos
- ✅ Cache hit rate > 80%
- ✅ Uptime > 99.5%
- ✅ Memory usage < 100MB

### 7. Cronograma de Implementação

#### Semana 1: Desenvolvimento Base
- **Dias 1-2**: BinomoDataProvider + testes básicos
- **Dias 3-4**: Sistema de configuração + cache
- **Dias 5-7**: Modificações no bot principal

#### Semana 2: Integração e Testes
- **Dias 1-3**: Testes unitários e integração
- **Dias 4-5**: Otimizações de performance
- **Dias 6-7**: Documentação e deploy

### 8. Métricas de Monitoramento

#### 8.1 Métricas Técnicas
- **API Response Time**: < 1000ms (P95)
- **Error Rate**: < 1%
- **Cache Hit Rate**: > 80%
- **Memory Usage**: < 100MB
- **CPU Usage**: < 50%

#### 8.2 Métricas de Negócio
- **Sinais Gerados/Hora**: Manter atual
- **Precisão dos Sinais**: Manter ou melhorar
- **Uptime do Bot**: > 99.5%
- **Tempo de Resposta Telegram**: < 3s

### 9. Plano de Rollback

#### 9.1 Cenários de Rollback
- **API Binomo indisponível** > 5 minutos
- **Error rate** > 5%
- **Latência** > 5 segundos
- **Falha crítica** no sistema

#### 9.2 Procedimento de Rollback
1. **Backup automático** do código atual
2. **Script de rollback** automatizado
3. **Restauração** de configurações MT5
4. **Validação** de funcionamento
5. **Notificação** de equipe

### 10. Considerações de Segurança

#### 10.1 Proteções Implementadas
- **Rate limiting** para evitar bloqueios
- **Timeout** em todas as requests
- **Validação** de dados recebidos
- **Logs** sem informações sensíveis
- **Error handling** robusto

#### 10.2 Monitoramento de Segurança
- **Alertas** para falhas de API
- **Monitoring** de rate limits
- **Logs** de tentativas de acesso
- **Backup** de configurações críticas
