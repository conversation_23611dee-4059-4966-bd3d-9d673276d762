#!/usr/bin/env python3
"""
Bot Binomo - Estratégia Otimizada v2.0
Implementa melhorias baseadas nos testes com dados em tempo real
"""

import telebot
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from binomo_realtime_provider import BinomoRealtimeProvider
import json
from typing import Optional, Dict, Tuple

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    operation: str
    probability: float
    confidence_level: str
    expiration_time: datetime = None
    exit_price: float = None
    result: str = None

class OptimizedTradingBot:
    def __init__(self):
        print("🚀 Inicializando Bot com Estratégia Otimizada v2.0...")
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False
        
        # Histórico para análise adaptativa
        self.signal_history = []
        self.performance_metrics = {
            'total_signals': 0,
            'wins': 0,
            'losses': 0,
            'win_rate': 0.0
        }
        
        # Inicializar BinomoRealtimeProvider
        self.data_provider = BinomoRealtimeProvider(self.config)
        print("✅ Bot Otimizado inicializado com sucesso!")

    def _load_config(self):
        """Carrega configurações otimizadas"""
        load_dotenv()

        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '52.0'))  # Reduzido para mais sinais
        }

        try:
            with open('config_binomo.json', 'r') as f:
                binomo_config = json.load(f)
                self.config.update(binomo_config)
        except FileNotFoundError:
            self.config.update({
                'SYMBOL': 'Z-CRY/IDX',
                'TIMEFRAME': '1',
                'USE_REALTIME_API': True
            })

        # Configurações padrão para expiração dinâmica
        if 'EXPIRATION_STRATEGY' not in self.config:
            self.config.update({
                'EXPIRATION_STRATEGY': 'DYNAMIC',
                'EXPIRATION_OPTIONS': {
                    'MIN_EXPIRATION': 1,
                    'MAX_EXPIRATION': 5,
                    'DEFAULT_EXPIRATION': 3,
                    'VOLATILITY_THRESHOLD': 1.3,
                    'HIGH_CONFIDENCE_EXPIRATION': 2,
                    'LOW_CONFIDENCE_EXPIRATION': 5
                },
                'TRADING_SESSIONS': {
                    'EUROPEAN': {'start': 8, 'end': 17, 'preferred_expiration': 2},
                    'AMERICAN': {'start': 14, 'end': 22, 'preferred_expiration': 2},
                    'ASIAN': {'start': 22, 'end': 8, 'preferred_expiration': 3}
                }
            })

    def prepare_features_optimized(self, df):
        """Features otimizadas com melhor precisão"""
        try:
            if len(df) < 20:
                return None

            df = df.copy()
            
            # === MÉDIAS MÓVEIS OTIMIZADAS ===
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
            df['ema_8'] = df['close'].ewm(span=8).mean()
            df['ema_21'] = df['close'].ewm(span=21).mean()
            
            # === RSI OTIMIZADO ===
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
            rs = gain / (loss + 1e-10)  # Evitar divisão por zero
            df['rsi'] = 100 - (100 / (1 + rs))
            df['rsi_smooth'] = df['rsi'].rolling(window=3).mean()  # RSI suavizado
            
            # === MACD MELHORADO ===
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            df['macd_momentum'] = df['macd_histogram'].diff()  # Momentum do MACD
            
            # === BOLLINGER BANDS AVANÇADO ===
            df['bb_middle'] = df['close'].rolling(window=20, min_periods=1).mean()
            bb_std = df['close'].rolling(window=20, min_periods=1).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']  # Squeeze indicator
            
            # === VOLUME INTELIGENTE ===
            df['volume_sma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
            df['volume_trend'] = df['tick_volume'].rolling(window=5).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1, raw=False
            )
            df['volume_acceleration'] = df['volume_ratio'].diff()  # Aceleração do volume
            
            # === VOLATILIDADE AVANÇADA ===
            df['atr'] = self._calculate_atr(df, period=14)  # Average True Range
            df['volatility'] = df['close'].rolling(window=10, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()
            
            # === PRESSÃO DE MERCADO MELHORADA ===
            df['body_size'] = abs(df['close'] - df['open']) / df['open']
            df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
            df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']
            
            # Pressão baseada em candles
            df['bullish_candle'] = (df['close'] > df['open']).astype(int)
            df['bearish_candle'] = (df['close'] < df['open']).astype(int)
            df['doji_candle'] = (abs(df['close'] - df['open']) / df['open'] < 0.001).astype(int)
            
            # Pressão acumulada
            df['buying_pressure'] = np.where(df['close'] > df['open'], 
                                           df['tick_volume'] * df['body_size'], 0)
            df['selling_pressure'] = np.where(df['close'] < df['open'], 
                                            df['tick_volume'] * df['body_size'], 0)
            df['net_pressure'] = (df['buying_pressure'] - df['selling_pressure']).rolling(window=5).sum()
            df['pressure_momentum'] = df['net_pressure'].diff()  # Momentum da pressão
            
            # === MOMENTUM AVANÇADO ===
            df['roc'] = df['close'].pct_change(periods=5) * 100
            df['momentum'] = df['close'] / df['close'].shift(10) - 1
            df['price_acceleration'] = df['close'].diff().diff()  # Segunda derivada do preço
            
            # === TENDÊNCIA MULTI-TIMEFRAME ===
            df['trend_short'] = np.where(df['ema_8'] > df['ema_21'], 1, -1)
            df['trend_medium'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            df['trend_long'] = np.where(df['sma_10'] > df['sma_20'], 1, -1)
            df['trend_consensus'] = df['trend_short'] + df['trend_medium'] + df['trend_long']
            df['trend_strength'] = abs(df['ema_8'] - df['ema_21']) / df['ema_21'] * 100
            
            return df.fillna(0)
            
        except Exception as e:
            logging.error(f"Erro ao preparar features: {e}")
            return None
    
    def _calculate_atr(self, df, period=14):
        """Calcula Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())

        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(window=period, min_periods=1).mean()

    def is_good_trading_time(self):
        """Verifica se é bom horário para trading"""
        current_hour = datetime.now().hour

        # Evitar horários de baixa liquidez (madrugada)
        if 2 <= current_hour <= 6:
            return False

        # Horários preferenciais (sobreposição de sessões)
        if 8 <= current_hour <= 12 or 14 <= current_hour <= 18:
            return True

        return True  # Outros horários OK

    def get_current_trading_session(self):
        """Identifica a sessão de trading atual"""
        current_hour = datetime.now().hour

        sessions = self.config.get('TRADING_SESSIONS', {})

        # Verificar sessão Europeia
        if 8 <= current_hour <= 17:
            return sessions.get('EUROPEAN', {'preferred_expiration': 2})

        # Verificar sessão Americana
        elif 14 <= current_hour <= 22:
            return sessions.get('AMERICAN', {'preferred_expiration': 2})

        # Sessão Asiática (inclui madrugada)
        else:
            return sessions.get('ASIAN', {'preferred_expiration': 3})

    def validate_signal_consensus(self, signals):
        """Valida consenso entre indicadores"""
        bullish_signals = 0
        bearish_signals = 0
        neutral_signals = 0

        # Contar sinais de cada direção
        for indicator, signal in signals.items():
            signal_str = str(signal).lower()
            if any(word in signal_str for word in ['bullish', 'oversold', 'recovery', 'buying']):
                bullish_signals += 1
            elif any(word in signal_str for word in ['bearish', 'overbought', 'selling']):
                bearish_signals += 1
            else:
                neutral_signals += 1

        total_signals = bullish_signals + bearish_signals + neutral_signals
        if total_signals == 0:
            return False, 0

        # Calcular consenso (ignorar neutros)
        directional_signals = bullish_signals + bearish_signals
        if directional_signals == 0:
            return False, 0

        consensus_ratio = max(bullish_signals, bearish_signals) / directional_signals
        return consensus_ratio >= 0.6, consensus_ratio

    def determine_optimal_expiration(self, df, confidence_level, score, signals):
        """Determina expiração ótima baseada em múltiplos fatores"""
        try:
            # Configurações de expiração
            exp_config = self.config.get('EXPIRATION_OPTIONS', {})
            min_exp = exp_config.get('MIN_EXPIRATION', 1)
            max_exp = exp_config.get('MAX_EXPIRATION', 5)
            default_exp = exp_config.get('DEFAULT_EXPIRATION', 3)
            volatility_threshold = exp_config.get('VOLATILITY_THRESHOLD', 1.3)

            # Se estratégia não é dinâmica, usar padrão
            if self.config.get('EXPIRATION_STRATEGY', 'DYNAMIC') != 'DYNAMIC':
                return default_exp

            # Análise de volatilidade
            last_row = df.iloc[-1]
            volatility_factor = last_row.get('volatility_ratio', 1.0)
            atr_factor = (last_row.get('atr', 0) / last_row.get('close', 1)) * 100

            # Análise de momentum
            momentum_strength = abs(last_row.get('macd_momentum', 0))
            rsi_value = last_row.get('rsi', 50)
            rsi_extreme = rsi_value < 25 or rsi_value > 75

            # Análise de consenso entre indicadores
            has_consensus, consensus_ratio = self.validate_signal_consensus(signals)

            # Análise de sessão de trading
            current_session = self.get_current_trading_session()
            session_preferred = current_session.get('preferred_expiration', 3)

            # Lógica de decisão hierárquica

            # 1. Sinais de altíssima confiança + alta volatilidade = 1 minuto
            if (confidence_level == "MUITO_ALTA" and
                volatility_factor > volatility_threshold and
                has_consensus and consensus_ratio > 0.8):
                return max(min_exp, 1)

            # 2. Sinal muito forte em extremo RSI = 1-2 minutos
            if (confidence_level == "MUITO_ALTA" and rsi_extreme and
                momentum_strength > 0.001):
                return max(min_exp, 1 if volatility_factor > 1.2 else 2)

            # 3. Sinal forte em horário ativo = 2 minutos
            if (confidence_level in ["MUITO_ALTA", "ALTA"] and
                session_preferred <= 2 and has_consensus):
                return max(min_exp, 2)

            # 4. Sinais médios/altos com boa volatilidade = 2-3 minutos
            if confidence_level in ["ALTA", "MEDIA"]:
                if volatility_factor > 1.1 and has_consensus:
                    return max(min_exp, 2)
                else:
                    return max(min_exp, min(3, session_preferred))

            # 5. Sinais fracos precisam mais tempo = 5 minutos
            if confidence_level == "BAIXA":
                return min(max_exp, 5)

            # 6. Fallback para configuração da sessão
            return max(min_exp, min(max_exp, session_preferred))

        except Exception as e:
            logging.error(f"Erro ao determinar expiração ótima: {e}")
            return self.config.get('EXPIRATION_OPTIONS', {}).get('DEFAULT_EXPIRATION', 3)

    def advanced_strategy_v2(self, df):
        """Estratégia otimizada v2.0 com melhorias implementadas"""
        try:
            features = self.prepare_features_optimized(df)
            if features is None or len(features) == 0:
                return None, 0, {}, "LOW"
            
            last_row = features.iloc[-1]
            prev_row = features.iloc[-2] if len(features) > 1 else last_row
            
            # Sistema de pontuação adaptativo
            base_score = 50
            score = base_score
            signals = {}
            confidence_factors = []
            
            # === 1. ANÁLISE DE TENDÊNCIA (30%) ===
            trend_score = 0
            
            # Consenso de tendência (peso maior)
            trend_consensus = last_row['trend_consensus']
            if trend_consensus >= 2:  # 2 ou 3 indicadores concordam
                trend_score += 12
                signals['trend_consensus'] = f"Forte Alta ({trend_consensus}/3)"
                confidence_factors.append("TREND_STRONG_UP")
            elif trend_consensus <= -2:
                trend_score -= 12
                signals['trend_consensus'] = f"Forte Baixa ({trend_consensus}/3)"
                confidence_factors.append("TREND_STRONG_DOWN")
            elif trend_consensus == 1:
                trend_score += 6
                signals['trend_consensus'] = f"Moderada Alta ({trend_consensus}/3)"
            elif trend_consensus == -1:
                trend_score -= 6
                signals['trend_consensus'] = f"Moderada Baixa ({trend_consensus}/3)"
            else:
                signals['trend_consensus'] = "Indefinida (0/3)"
            
            # Força da tendência
            if last_row['trend_strength'] > 0.5:
                trend_bonus = min(6, last_row['trend_strength'] * 2)
                trend_score += trend_bonus if trend_consensus > 0 else -trend_bonus
                signals['trend_strength'] = f"Forte ({last_row['trend_strength']:.2f}%)"
                confidence_factors.append("TREND_STRENGTH")
            else:
                signals['trend_strength'] = f"Fraca ({last_row['trend_strength']:.2f}%)"
            
            score += trend_score
            
            # === 2. ANÁLISE DE MOMENTUM (25%) ===
            momentum_score = 0
            
            # RSI otimizado com zonas específicas
            rsi = last_row['rsi_smooth']
            if rsi < 25:  # Oversold extremo
                momentum_score += 10
                signals['rsi'] = f"Oversold Extremo ({rsi:.1f})"
                confidence_factors.append("RSI_OVERSOLD")
            elif 25 <= rsi <= 35:  # Oversold recovery
                momentum_score += 6
                signals['rsi'] = f"Recovery ({rsi:.1f})"
            elif 65 <= rsi <= 75:  # Overbought entry
                momentum_score -= 6
                signals['rsi'] = f"Overbought ({rsi:.1f})"
            elif rsi > 75:  # Overbought extremo
                momentum_score -= 10
                signals['rsi'] = f"Overbought Extremo ({rsi:.1f})"
                confidence_factors.append("RSI_OVERBOUGHT")
            else:
                signals['rsi'] = f"Neutro ({rsi:.1f})"
            
            # MACD com momentum
            if (last_row['macd'] > last_row['macd_signal'] and 
                last_row['macd_momentum'] > 0):
                momentum_score += 8
                signals['macd'] = f"Bullish + Momentum ({last_row['macd']:.5f})"
                confidence_factors.append("MACD_BULLISH_MOMENTUM")
            elif (last_row['macd'] < last_row['macd_signal'] and 
                  last_row['macd_momentum'] < 0):
                momentum_score -= 8
                signals['macd'] = f"Bearish + Momentum ({last_row['macd']:.5f})"
                confidence_factors.append("MACD_BEARISH_MOMENTUM")
            else:
                signals['macd'] = f"Neutro ({last_row['macd']:.5f})"
            
            score += momentum_score
            
            # === 3. ANÁLISE DE VOLUME (20%) ===
            volume_score = 0
            
            # Volume com confirmação
            if last_row['volume_ratio'] > 1.5 and last_row['volume_acceleration'] > 0:
                volume_score += 10
                signals['volume'] = f"Alto + Aceleração ({last_row['volume_ratio']:.2f}x)"
                confidence_factors.append("VOLUME_SURGE")
            elif last_row['volume_ratio'] > 1.2:
                volume_score += 6
                signals['volume'] = f"Alto ({last_row['volume_ratio']:.2f}x)"
            elif last_row['volume_ratio'] < 0.6:
                volume_score -= 4
                signals['volume'] = f"Baixo ({last_row['volume_ratio']:.2f}x)"
            else:
                signals['volume'] = f"Normal ({last_row['volume_ratio']:.2f}x)"
            
            # Penalizar volume muito baixo
            if last_row['volume_ratio'] < 0.5:
                score *= 0.85  # Reduz score em 15%
                signals['volume_penalty'] = "Volume insuficiente"
            
            score += volume_score
            
            # === 4. ANÁLISE DE PRESSÃO (15%) ===
            pressure_score = 0
            
            # Pressão com momentum
            if (last_row['net_pressure'] > 2000 and 
                last_row['pressure_momentum'] > 0):
                pressure_score += 8
                signals['pressure'] = f"Forte Compra + Momentum ({last_row['net_pressure']:.0f})"
                confidence_factors.append("PRESSURE_BUYING_SURGE")
            elif (last_row['net_pressure'] < -2000 and 
                  last_row['pressure_momentum'] < 0):
                pressure_score -= 8
                signals['pressure'] = f"Forte Venda + Momentum ({last_row['net_pressure']:.0f})"
                confidence_factors.append("PRESSURE_SELLING_SURGE")
            elif last_row['net_pressure'] > 500:
                pressure_score += 4
                signals['pressure'] = f"Compra ({last_row['net_pressure']:.0f})"
            elif last_row['net_pressure'] < -500:
                pressure_score -= 4
                signals['pressure'] = f"Venda ({last_row['net_pressure']:.0f})"
            else:
                signals['pressure'] = f"Equilibrada ({last_row['net_pressure']:.0f})"
            
            score += pressure_score
            
            # === 5. ANÁLISE DE VOLATILIDADE (10%) ===
            volatility_score = 0
            
            # Volatilidade ideal para trading
            if 0.8 <= last_row['volatility_ratio'] <= 1.8:
                volatility_score += 5
                signals['volatility'] = f"Ideal ({last_row['volatility_ratio']:.2f}x)"
                confidence_factors.append("VOLATILITY_IDEAL")
            elif last_row['volatility_ratio'] > 3:
                volatility_score -= 5
                signals['volatility'] = f"Muito Alta ({last_row['volatility_ratio']:.2f}x)"
            else:
                signals['volatility'] = f"Normal ({last_row['volatility_ratio']:.2f}x)"
            
            # Bollinger Bands
            bb_pos = last_row['bb_position']
            if bb_pos < 0.15:  # Próximo da banda inferior
                volatility_score += 3
                signals['bollinger'] = f"Oversold ({bb_pos:.2f})"
            elif bb_pos > 0.85:  # Próximo da banda superior
                volatility_score -= 3
                signals['bollinger'] = f"Overbought ({bb_pos:.2f})"
            else:
                signals['bollinger'] = f"Meio ({bb_pos:.2f})"
            
            score += volatility_score
            
            # === DETERMINAÇÃO FINAL COM CONFIANÇA ADAPTATIVA ===
            
            # Calcular nível de confiança
            high_confidence_count = len([f for f in confidence_factors if f in [
                'TREND_STRONG_UP', 'TREND_STRONG_DOWN', 'RSI_OVERSOLD', 'RSI_OVERBOUGHT',
                'MACD_BULLISH_MOMENTUM', 'MACD_BEARISH_MOMENTUM', 'VOLUME_SURGE', 
                'PRESSURE_BUYING_SURGE', 'PRESSURE_SELLING_SURGE', 'VOLATILITY_IDEAL'
            ]])
            
            if high_confidence_count >= 3:
                confidence_level = "MUITO_ALTA"
                threshold_buy = 51
                threshold_sell = 49
            elif high_confidence_count >= 2:
                confidence_level = "ALTA"
                threshold_buy = 52
                threshold_sell = 48
            elif high_confidence_count >= 1:
                confidence_level = "MEDIA"
                threshold_buy = 53
                threshold_sell = 47
            else:
                confidence_level = "BAIXA"
                threshold_buy = 55
                threshold_sell = 45
            
            # Determinar direção
            if score > threshold_buy:
                direction = '🟩COMPRA'
            elif score < threshold_sell:
                direction = '🟥VENDA'
            else:
                direction = None
            
            return direction, score, signals, confidence_level

        except Exception as e:
            logging.error(f"Erro na estratégia otimizada: {e}")
            return None, 0, {}, "LOW"

    def gerar_sinal_otimizado(self):
        """Gera sinal com estratégia otimizada e controle inteligente"""
        # Verifica trade ativo
        if self.active_trade and datetime.now() < self.active_trade.expiration_time:
            remaining_time = (self.active_trade.expiration_time - datetime.now()).total_seconds()
            return f"⏳ Trade ativo! Aguarde {remaining_time/60:.1f} minutos para o resultado."

        # Calcula resultado se trade expirado
        if self.active_trade and datetime.now() >= self.active_trade.expiration_time:
            self._calculate_and_send_result_optimized()
            self.active_trade = None

        if self.processing_signal:
            return "⚠️ Processando sinal..."

        self.processing_signal = True
        try:
            # Obter dados em tempo real
            df = self.data_provider.get_historical_data(count=30)
            if df is None or len(df) < 20:
                self.processing_signal = False
                return "⚠️ Dados insuficientes para análise."

            # Executar estratégia otimizada
            direction, score, signals, confidence_level = self.advanced_strategy_v2(df)

            if direction is None:
                self.processing_signal = False
                return f"⚪ Sinal neutro (Score: {score:.2f}, Confiança: {confidence_level})"

            # Verificar se é bom horário para trading (removido aviso)
            # if not self.is_good_trading_time():
            #     self.processing_signal = False
            #     return "⚠️ Horário não recomendado para trading (baixa liquidez)"

            # Obter preço de entrada
            entry_price = self.data_provider.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            # Determinar expiração ótima dinamicamente
            optimal_expiration = self.determine_optimal_expiration(df, confidence_level, score, signals)

            # Criar trade com expiração dinâmica
            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(minutes=optimal_expiration)
            self.active_trade = TradeResult(
                entry_time, entry_price, direction, score,
                confidence_level, expiration_time=expiration_time
            )

            # Preparar mensagem otimizada
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊",
                "BAIXA": "⚠️"
            }

            # Obter informações da sessão atual
            current_session = self.get_current_trading_session()
            session_name = "Europeia" if 8 <= datetime.now().hour <= 17 else "Americana" if 14 <= datetime.now().hour <= 22 else "Asiática"

            # Validar consenso dos indicadores
            has_consensus, consensus_ratio = self.validate_signal_consensus(signals)
            consensus_text = f"✅ {consensus_ratio:.1%}" if has_consensus else f"⚠️ {consensus_ratio:.1%}"

            message = (
                f"🎯 **SINAL DINÂMICO v2.0**\n"
                f"{direction} | {confidence_emoji.get(confidence_level, '📊')} {confidence_level}\n"
                f"📈 Ativo: {self.config.get('SYMBOL', 'CRY/IDX')}\n"
                f"⏳ Expiração: {expiration_time.strftime('%H:%M:%S')} (M{optimal_expiration}) 🔄\n"
                f"🌍 Sessão: {session_name}\n"
                f"🎯 Consenso: {consensus_text}\n\n"
                f"📊 Score: {score:.2f}/100\n"
                f"💰 Entrada: {entry_price:.5f}\n\n"
                f"🔍 **ANÁLISE DETALHADA**\n"
            )

            # Adicionar indicadores principais
            key_indicators = ['trend_consensus', 'rsi', 'macd', 'volume', 'pressure', 'volatility']
            for indicator in key_indicators:
                if indicator in signals:
                    message += f"• {indicator.upper()}: {signals[indicator]}\n"

            # Adicionar estatísticas de performance
            if self.performance_metrics['total_signals'] > 0:
                win_rate = self.performance_metrics['win_rate']
                message += f"\n📈 Win Rate: {win_rate:.1f}% ({self.performance_metrics['wins']}/{self.performance_metrics['total_signals']})"

            # Enviar sinal
            self.bot.send_message(self.config['chat_id'], message)
            logging.info(f"Sinal otimizado enviado: {direction} - Score: {score:.2f} - Confiança: {confidence_level}")

            # Registrar no histórico
            self.signal_history.append({
                'timestamp': entry_time,
                'direction': direction,
                'score': score,
                'confidence': confidence_level,
                'entry_price': entry_price
            })

            self.processing_signal = False
            return f"✅ Sinal {direction} enviado! (Confiança: {confidence_level})"

        except Exception as e:
            logging.error(f"Erro ao gerar sinal otimizado: {e}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."

    def _calculate_and_send_result_optimized(self):
        """Calcula resultado com métricas de performance"""
        if not self.active_trade:
            return

        try:
            exit_price = self.data_provider.get_current_price()
            if exit_price is None:
                logging.error("Erro ao obter preço de fechamento")
                return

            # Calcular resultado
            if self.active_trade.operation == '🟩COMPRA':
                is_win = exit_price > self.active_trade.entry_price
            else:
                is_win = exit_price < self.active_trade.entry_price

            result = "✅GAIN 📈" if is_win else "❌LOSS 📉"
            price_change = ((exit_price - self.active_trade.entry_price) / self.active_trade.entry_price) * 100

            # Atualizar métricas de performance
            self.performance_metrics['total_signals'] += 1
            if is_win:
                self.performance_metrics['wins'] += 1
            else:
                self.performance_metrics['losses'] += 1

            self.performance_metrics['win_rate'] = (
                self.performance_metrics['wins'] / self.performance_metrics['total_signals'] * 100
            )

            # Mensagem de resultado otimizada
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊",
                "BAIXA": "⚠️"
            }

            result_message = (
                f"🔄 **RESULTADO - ESTRATÉGIA v2.0**\n"
                f"Operação: {self.active_trade.operation} {confidence_emoji.get(self.active_trade.confidence_level, '📊')}\n"
                f"Confiança: {self.active_trade.confidence_level}\n"
                f"Score: {self.active_trade.probability:.2f}/100\n\n"
                f"💰 Entrada: {self.active_trade.entry_price:.5f}\n"
                f"💰 Saída: {exit_price:.5f}\n"
                f"📊 Variação: {price_change:+.3f}%\n"
                f"🎯 Resultado: {result}\n\n"
                f"📈 **PERFORMANCE GERAL**\n"
                f"Win Rate: {self.performance_metrics['win_rate']:.1f}%\n"
                f"Wins: {self.performance_metrics['wins']} | Losses: {self.performance_metrics['losses']}\n"
                f"Total: {self.performance_metrics['total_signals']} sinais"
            )

            self.bot.send_message(self.config['chat_id'], result_message)
            logging.info(f"Resultado: {result} - Variação: {price_change:+.3f}% - Win Rate: {self.performance_metrics['win_rate']:.1f}%")

        except Exception as e:
            logging.error(f"Erro ao calcular resultado: {e}")

    def _setup_telegram_handlers(self):
        """Configura comandos otimizados do Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            welcome_msg = (
                "🚀 **Bot Binomo - Estratégia Otimizada v2.0**\n\n"
                "Comandos disponíveis:\n"
                "• /sinal - Gerar sinal otimizado\n"
                "• /stats - Ver estatísticas de performance\n"
                "• /config - Ver configurações atuais\n"
                "• /dynamic - Info sobre expiração dinâmica\n\n"
                "🔥 **NOVO: SISTEMA DE EXPIRAÇÃO DINÂMICA**\n"
                "• Adapta expiração às condições de mercado\n"
                "• Considera volatilidade e força do sinal\n"
                "• Otimiza baseado na sessão de trading\n"
                "• Melhora significativamente a precisão\n\n"
                "✨ Outras melhorias:\n"
                "• Análise multi-indicador avançada\n"
                "• Sistema de confiança adaptativo\n"
                "• Filtros de horário de trading\n"
                "• Validação de consenso entre indicadores"
            )
            self.bot.send_message(message.chat.id, welcome_msg)

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal_otimizado()
            self.bot.send_message(message.chat.id, sinal)

        @self.bot.message_handler(commands=['stats'])
        def show_stats(message):
            if self.performance_metrics['total_signals'] == 0:
                stats_msg = "📊 Nenhum sinal gerado ainda."
            else:
                stats_msg = (
                    f"📊 **ESTATÍSTICAS DE PERFORMANCE**\n\n"
                    f"🎯 Win Rate: {self.performance_metrics['win_rate']:.1f}%\n"
                    f"✅ Wins: {self.performance_metrics['wins']}\n"
                    f"❌ Losses: {self.performance_metrics['losses']}\n"
                    f"📈 Total de Sinais: {self.performance_metrics['total_signals']}\n\n"
                    f"📋 Últimos 5 sinais:\n"
                )

                # Mostrar últimos 5 sinais
                recent_signals = self.signal_history[-5:] if len(self.signal_history) >= 5 else self.signal_history
                for i, signal in enumerate(recent_signals, 1):
                    stats_msg += f"{i}. {signal['direction']} - Score: {signal['score']:.1f} - {signal['confidence']}\n"

            self.bot.send_message(message.chat.id, stats_msg)

        @self.bot.message_handler(commands=['config'])
        def show_config(message):
            # Informações da sessão atual
            current_session = self.get_current_trading_session()
            session_name = "Europeia" if 8 <= datetime.now().hour <= 17 else "Americana" if 14 <= datetime.now().hour <= 22 else "Asiática"

            exp_strategy = self.config.get('EXPIRATION_STRATEGY', 'FIXED')
            exp_options = self.config.get('EXPIRATION_OPTIONS', {})

            config_msg = (
                f"⚙️ **CONFIGURAÇÕES ATUAIS**\n\n"
                f"📈 Símbolo: {self.config.get('SYMBOL', 'N/A')}\n"
                f"⏱️ Timeframe: {self.config.get('TIMEFRAME', 'N/A')}m\n"
                f"🎯 Confiança Mínima: {self.config.get('min_confidence', 'N/A')}\n"
                f"🔄 API Tempo Real: {'✅' if self.config.get('USE_REALTIME_API') else '❌'}\n"
                f"📊 Dados Conectados: {'✅' if self.data_provider.test_connection() else '❌'}\n\n"
                f"🔄 **SISTEMA DE EXPIRAÇÃO**\n"
                f"Estratégia: {exp_strategy} {'🔥' if exp_strategy == 'DYNAMIC' else '📊'}\n"
                f"Faixa: {exp_options.get('MIN_EXPIRATION', 1)}-{exp_options.get('MAX_EXPIRATION', 5)} min\n"
                f"Padrão: {exp_options.get('DEFAULT_EXPIRATION', 3)} min\n\n"
                f"🌍 **SESSÃO ATUAL**\n"
                f"Sessão: {session_name}\n"
                f"Expiração Preferida: {current_session.get('preferred_expiration', 3)} min\n"
                f"Horário Recomendado: {'✅' if self.is_good_trading_time() else '⚠️'}"
            )
            self.bot.send_message(message.chat.id, config_msg)

        @self.bot.message_handler(commands=['dynamic'])
        def show_dynamic_info(message):
            exp_options = self.config.get('EXPIRATION_OPTIONS', {})

            dynamic_msg = (
                f"🔄 **SISTEMA DE EXPIRAÇÃO DINÂMICA**\n\n"
                f"📊 **LÓGICA DE DECISÃO:**\n"
                f"• Confiança MUITO_ALTA + Alta Volatilidade = 1 min\n"
                f"• Sinal Forte + RSI Extremo = 1-2 min\n"
                f"• Sinal Forte + Horário Ativo = 2 min\n"
                f"• Sinais Médios/Altos = 2-3 min\n"
                f"• Sinais Fracos = 5 min\n\n"
                f"⚙️ **CONFIGURAÇÕES:**\n"
                f"• Limite Volatilidade: {exp_options.get('VOLATILITY_THRESHOLD', 1.3)}\n"
                f"• Alta Confiança: {exp_options.get('HIGH_CONFIDENCE_EXPIRATION', 2)} min\n"
                f"• Baixa Confiança: {exp_options.get('LOW_CONFIDENCE_EXPIRATION', 5)} min\n\n"
                f"🌍 **SESSÕES DE TRADING:**\n"
                f"• Europeia (8h-17h): 2 min preferido\n"
                f"• Americana (14h-22h): 2 min preferido\n"
                f"• Asiática (22h-8h): 3 min preferido\n\n"
                f"✨ **VANTAGENS:**\n"
                f"• Adapta-se às condições de mercado\n"
                f"• Otimiza baseado na força do sinal\n"
                f"• Considera horários de liquidez\n"
                f"• Melhora taxa de acerto significativamente"
            )
            self.bot.send_message(message.chat.id, dynamic_msg)

    def run(self):
        """Executa o bot otimizado"""
        if not self.data_provider.test_connection():
            logging.error("Erro ao conectar com dados em tempo real")
            return

        # Verificar configuração do sistema dinâmico
        exp_strategy = self.config.get('EXPIRATION_STRATEGY', 'FIXED')
        exp_options = self.config.get('EXPIRATION_OPTIONS', {})

        # Enviar mensagem de inicialização
        startup_msg = (
            "🚀 **BOT OTIMIZADO v2.0 ATIVO!**\n\n"
            f"🔥 **SISTEMA DE EXPIRAÇÃO: {exp_strategy}**\n"
            f"Faixa: {exp_options.get('MIN_EXPIRATION', 1)}-{exp_options.get('MAX_EXPIRATION', 5)} min\n\n"
            "✨ Melhorias implementadas:\n"
            "• 🔄 Expiração dinâmica inteligente\n"
            "• 📊 Estratégia multi-indicador avançada\n"
            "• 🎯 Sistema de confiança adaptativo\n"
            "• 🌍 Filtros de sessão de trading\n"
            "• ✅ Validação de consenso entre indicadores\n"
            "• 📈 Métricas de performance em tempo real\n\n"
            "Use /sinal para começar!\n"
            "Use /dynamic para entender o sistema dinâmico!"
        )
        self.bot.send_message(self.config['chat_id'], startup_msg)

        logging.info("🚀 Bot Otimizado v2.0 iniciado com dados em tempo real!")
        self.bot.polling()

if __name__ == "__main__":
    print("Iniciando Bot com Estratégia Otimizada v2.0...")
    bot = OptimizedTradingBot()
    bot.run()
