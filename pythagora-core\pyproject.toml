[tool.poetry]
name = "pythagora-core"
version = "2.0.10"
description = "Build complete apps using AI agents"
authors = ["<PERSON> <<EMAIL>>"]
license = "FSL-1.1-MIT"
readme = "README.md"
packages = [{include = "core", from = "."}]
repository = "https://github.com/Pythagora-io/pythagora-core"
homepage = "https://pythagora.ai"
keywords = ["ai", "llm"]

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "testpypi"
url = "https://test.pypi.org/legacy/"
priority = "explicit"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.dependencies]
python = "^3.9"
pydantic = "^2.8.2"
openai = "^1.40.6"
anthropic = "^0.39.0"
groq = "^0.6.0"
jinja2 = "^3.1.4"
tiktoken = "^0.8.0"
sqlalchemy = { version = "^2.0.32", extras = ["asyncio"] }
aiosqlite = "^0.20.0"
psutil = "^5.9.8"
httpx = "^0.27.0"
alembic = "^1.13.2"
python-dotenv = "^1.0.1"
prompt-toolkit = "^3.0.48"
jsonref = "^1.1.0"
tenacity = "9.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.1.1"
ruff = "^0.3.5"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.23.6"
pytest-timeout = "^2.3.1"
pre-commit = "^3.7.0"

[tool.pytest.ini_options]
addopts = "-ra -q --cov=core --no-cov-on-fail --timeout 10"
pythonpath = ["."]

[tool.coverage.report]
exclude_lines = ["if TYPE_CHECKING:", "raise NotImplementedError()"]
omit = ["core/db/migrations/*", "core/templates/tpl/*"]

[tool.ruff]
line-length = 120
indent-width = 4
target-version = "py39"
lint.extend-select = ["I"]
