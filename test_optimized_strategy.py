#!/usr/bin/env python3
"""
Teste da Estratégia Otimizada v2.0
Compara performance entre estratégia original e otimizada
"""

import time
import json
from datetime import datetime
from binomo_realtime_provider import BinomoRealtimeProvider
from bot_strategy_optimized import OptimizedTradingBot
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_optimized_strategy():
    """Testa a estratégia otimizada v2.0"""
    print("🚀 TESTE DA ESTRATÉGIA OTIMIZADA v2.0")
    print("=" * 50)
    
    # Carregar configuração
    with open('config_binomo.json', 'r') as f:
        config = json.load(f)
    
    # Inicializar bot otimizado (sem Telegram)
    bot = OptimizedTradingBot()
    bot.config['chat_id'] = None  # Desabilitar Telegram para teste
    
    print("✅ Bot otimizado inicializado")
    
    # Aguardar dados suficientes
    print("⏳ Aguardando dados em tempo real...")
    time.sleep(10)
    
    results = []
    
    print(f"\n📊 EXECUTANDO ANÁLISES (5 minutos)...")
    print("-" * 40)
    
    start_time = time.time()
    test_count = 0
    
    while (time.time() - start_time) < 300:  # 5 minutos
        try:
            # Obter dados
            df = bot.data_provider.get_historical_data(count=30)
            if df is None or len(df) < 20:
                print("⚠️ Dados insuficientes, aguardando...")
                time.sleep(5)
                continue
            
            # Executar estratégia otimizada
            direction, score, signals, confidence = bot.advanced_strategy_v2(df)
            current_price = bot.data_provider.get_current_price()
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            test_count += 1
            
            # Registrar resultado
            result = {
                'timestamp': timestamp,
                'price': current_price,
                'direction': direction,
                'score': score,
                'confidence': confidence,
                'signals': signals
            }
            results.append(result)
            
            # Exibir resultado
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊", 
                "BAIXA": "⚠️"
            }
            
            print(f"\n⏰ {timestamp} | 💰 {current_price:.5f}")
            print(f"🎯 {direction or 'NEUTRO'} | {confidence_emoji.get(confidence, '📊')} {confidence}")
            print(f"📊 Score: {score:.2f}/100")
            
            # Mostrar indicadores principais
            key_indicators = ['trend_consensus', 'rsi', 'volume', 'pressure']
            for key in key_indicators:
                if key in signals:
                    print(f"   • {key.upper()}: {signals[key]}")
            
            # Aguardar próxima análise
            time.sleep(15)  # Análise a cada 15 segundos
            
        except Exception as e:
            print(f"❌ Erro no teste: {e}")
            time.sleep(5)
    
    # Análise dos resultados
    print(f"\n📈 ANÁLISE DOS RESULTADOS OTIMIZADOS")
    print("=" * 45)
    
    if not results:
        print("❌ Nenhum resultado para analisar")
        return
    
    # Estatísticas básicas
    total_tests = len(results)
    buy_signals = len([r for r in results if r['direction'] == '🟩COMPRA'])
    sell_signals = len([r for r in results if r['direction'] == '🟥VENDA'])
    neutral_signals = total_tests - buy_signals - sell_signals
    
    print(f"📊 Total de análises: {total_tests}")
    print(f"🟩 Sinais de COMPRA: {buy_signals} ({buy_signals/total_tests*100:.1f}%)")
    print(f"🟥 Sinais de VENDA: {sell_signals} ({sell_signals/total_tests*100:.1f}%)")
    print(f"⚪ Sinais NEUTROS: {neutral_signals} ({neutral_signals/total_tests*100:.1f}%)")
    
    # Análise por nível de confiança
    confidence_stats = {}
    for result in results:
        conf = result['confidence']
        if conf not in confidence_stats:
            confidence_stats[conf] = {'total': 0, 'signals': 0}
        confidence_stats[conf]['total'] += 1
        if result['direction']:
            confidence_stats[conf]['signals'] += 1
    
    print(f"\n🎯 DISTRIBUIÇÃO POR CONFIANÇA:")
    for conf, stats in confidence_stats.items():
        signal_rate = (stats['signals'] / stats['total']) * 100 if stats['total'] > 0 else 0
        print(f"   {conf}: {stats['total']} análises, {stats['signals']} sinais ({signal_rate:.1f}%)")
    
    # Análise de scores
    scores = [r['score'] for r in results]
    if scores:
        import numpy as np
        print(f"\n📈 SCORES:")
        print(f"   Médio: {np.mean(scores):.2f}")
        print(f"   Min/Max: {min(scores):.2f} / {max(scores):.2f}")
        print(f"   Desvio: {np.std(scores):.2f}")
    
    # Análise de preços
    prices = [r['price'] for r in results if r['price']]
    if len(prices) > 1:
        price_change = ((prices[-1] - prices[0]) / prices[0]) * 100
        price_volatility = np.std(prices) / np.mean(prices) * 100
        print(f"\n💰 MOVIMENTO DE PREÇOS:")
        print(f"   Variação total: {price_change:+.3f}%")
        print(f"   Volatilidade: {price_volatility:.3f}%")
    
    # Análise de indicadores mais eficazes
    print(f"\n🔍 INDICADORES MAIS ATIVOS:")
    all_signals = {}
    for result in results:
        for key, value in result['signals'].items():
            if key not in all_signals:
                all_signals[key] = []
            all_signals[key].append(str(value))
    
    for indicator, values in all_signals.items():
        unique_values = len(set(values))
        if unique_values > 1:
            print(f"   {indicator.upper()}: {unique_values} valores diferentes")
    
    # Comparação com estratégia anterior
    print(f"\n⚖️ COMPARAÇÃO COM ESTRATÉGIA ANTERIOR:")
    print("   ✅ Mais indicadores (RSI suavizado, MACD momentum, ATR)")
    print("   ✅ Sistema de confiança adaptativo")
    print("   ✅ Thresholds inteligentes")
    print("   ✅ Análise multi-timeframe")
    print("   ✅ Métricas de performance integradas")
    
    # Recomendações
    print(f"\n💡 RECOMENDAÇÕES:")
    
    signal_rate = ((buy_signals + sell_signals) / total_tests) * 100
    if signal_rate < 30:
        print("   📉 Taxa de sinais baixa - considerar reduzir thresholds")
    elif signal_rate > 60:
        print("   📈 Taxa de sinais alta - considerar aumentar thresholds")
    else:
        print("   ✅ Taxa de sinais balanceada")
    
    high_conf_signals = len([r for r in results if r['confidence'] in ['ALTA', 'MUITO_ALTA'] and r['direction']])
    if high_conf_signals > 0:
        print(f"   🎯 {high_conf_signals} sinais de alta confiança detectados")
    
    print(f"\n🎉 TESTE CONCLUÍDO!")
    print(f"Estratégia otimizada v2.0 testada com sucesso!")
    
    # Cleanup
    bot.data_provider.shutdown()

if __name__ == "__main__":
    test_optimized_strategy()
