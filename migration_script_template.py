#!/usr/bin/env python3
"""
Script de Migração Automatizada - MT5 para API Binomo
Automatiza o processo de migração do bot para usar API Binomo
"""

import os
import shutil
import json
import sys
from datetime import datetime
from pathlib import Path
import logging

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BinomoMigration:
    """Classe para gerenciar migração para API Binomo"""
    
    def __init__(self):
        self.backup_dir = Path(f"backup_mt5_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.current_dir = Path.cwd()
        self.success_steps = []
        self.failed_steps = []
    
    def create_backup(self):
        """Cria backup completo do código atual"""
        try:
            logger.info("🔄 Criando backup do código atual...")
            
            # Cria diretório de backup
            self.backup_dir.mkdir(exist_ok=True)
            
            # Lista de arquivos para backup
            files_to_backup = [
                'bot.binomo.py',
                '.env',
                'requirements.txt'
            ]
            
            # Faz backup dos arquivos
            for file_name in files_to_backup:
                file_path = self.current_dir / file_name
                if file_path.exists():
                    shutil.copy2(file_path, self.backup_dir / file_name)
                    logger.info(f"  ✅ Backup: {file_name}")
                else:
                    logger.warning(f"  ⚠️ Arquivo não encontrado: {file_name}")
            
            # Backup da pasta API se existir
            api_dir = self.current_dir / "API"
            if api_dir.exists():
                shutil.copytree(api_dir, self.backup_dir / "API")
                logger.info("  ✅ Backup: pasta API/")
            
            logger.info(f"✅ Backup criado em: {self.backup_dir}")
            self.success_steps.append("Backup criado")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar backup: {e}")
            self.failed_steps.append(f"Backup: {e}")
            return False
    
    def validate_environment(self):
        """Valida ambiente antes da migração"""
        try:
            logger.info("🔍 Validando ambiente...")
            
            # Verifica arquivos necessários
            required_files = ['bot.binomo.py', '.env']
            missing_files = []
            
            for file_name in required_files:
                if not (self.current_dir / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                logger.error(f"❌ Arquivos obrigatórios ausentes: {missing_files}")
                self.failed_steps.append(f"Arquivos ausentes: {missing_files}")
                return False
            
            # Verifica se Python tem as bibliotecas necessárias
            try:
                import requests
                import pandas as pd
                import telebot
                logger.info("  ✅ Bibliotecas necessárias disponíveis")
            except ImportError as e:
                logger.error(f"❌ Biblioteca ausente: {e}")
                self.failed_steps.append(f"Biblioteca ausente: {e}")
                return False
            
            # Verifica conectividade com API Binomo
            try:
                import requests
                response = requests.get("https://api.binomo.com", timeout=5)
                logger.info("  ✅ API Binomo acessível")
            except Exception as e:
                logger.warning(f"  ⚠️ Problema de conectividade: {e}")
            
            logger.info("✅ Ambiente validado")
            self.success_steps.append("Ambiente validado")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na validação: {e}")
            self.failed_steps.append(f"Validação: {e}")
            return False
    
    def create_binomo_files(self):
        """Cria arquivos necessários para API Binomo"""
        try:
            logger.info("📁 Criando arquivos da API Binomo...")
            
            # 1. Cria BinomoDataProvider
            if not (self.current_dir / "binomo_data_provider.py").exists():
                # Copia do template
                template_file = self.current_dir / "binomo_data_provider_template.py"
                if template_file.exists():
                    shutil.copy2(template_file, self.current_dir / "binomo_data_provider.py")
                    logger.info("  ✅ binomo_data_provider.py criado")
                else:
                    logger.error("  ❌ Template binomo_data_provider_template.py não encontrado")
                    return False
            
            # 2. Cria configuração
            if not (self.current_dir / "config_binomo.json").exists():
                template_config = self.current_dir / "config_binomo_template.json"
                if template_config.exists():
                    shutil.copy2(template_config, self.current_dir / "config_binomo.json")
                    logger.info("  ✅ config_binomo.json criado")
                else:
                    # Cria configuração básica
                    basic_config = {
                        "BINOMO_API_BASE": "https://api.binomo.com",
                        "SYMBOL": "Z-CRY%2FIDX",
                        "TIMEFRAME": "1",
                        "LOCALE": "br",
                        "REQUEST_TIMEOUT": 10,
                        "MAX_RETRIES": 3,
                        "CACHE_DURATION": 60,
                        "RATE_LIMIT_PER_MINUTE": 10
                    }
                    
                    with open(self.current_dir / "config_binomo.json", 'w') as f:
                        json.dump(basic_config, f, indent=2)
                    logger.info("  ✅ config_binomo.json criado (configuração básica)")
            
            # 3. Atualiza requirements.txt
            self.update_requirements()
            
            logger.info("✅ Arquivos da API Binomo criados")
            self.success_steps.append("Arquivos Binomo criados")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar arquivos: {e}")
            self.failed_steps.append(f"Criação de arquivos: {e}")
            return False
    
    def update_requirements(self):
        """Atualiza requirements.txt removendo MT5"""
        try:
            logger.info("📦 Atualizando requirements.txt...")
            
            requirements_file = self.current_dir / "requirements.txt"
            
            # Requirements atualizados (sem MT5)
            new_requirements = [
                "telebot==0.0.5",
                "pandas==2.0.3", 
                "numpy==1.24.3",
                "scikit-learn==1.3.0",
                "python-dotenv==1.0.0",
                "requests==2.31.0",
                "python-telegram-bot==20.3"
            ]
            
            with open(requirements_file, 'w') as f:
                f.write('\n'.join(new_requirements))
            
            logger.info("  ✅ requirements.txt atualizado (MT5 removido)")
            
        except Exception as e:
            logger.warning(f"  ⚠️ Erro ao atualizar requirements.txt: {e}")
    
    def modify_bot_file(self):
        """Modifica bot.binomo.py para usar API Binomo"""
        try:
            logger.info("🔧 Modificando bot.binomo.py...")
            
            bot_file = self.current_dir / "bot.binomo.py"
            
            # Lê arquivo atual
            with open(bot_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Cria versão modificada
            modified_file = self.current_dir / "bot.binomo_new.py"
            
            # Aplica modificações básicas
            modified_content = content
            
            # Remove import MT5
            modified_content = modified_content.replace(
                "import MetaTrader5 as mt5",
                "# import MetaTrader5 as mt5  # REMOVIDO - Migração para API Binomo"
            )
            
            # Adiciona import do BinomoDataProvider
            if "from binomo_data_provider import BinomoDataProvider" not in modified_content:
                import_section = "import logging\nfrom dataclasses import dataclass"
                new_import_section = import_section + "\nfrom binomo_data_provider import BinomoDataProvider\nimport json"
                modified_content = modified_content.replace(import_section, new_import_section)
            
            # Salva versão modificada
            with open(modified_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            logger.info("  ✅ bot.binomo_new.py criado com modificações básicas")
            logger.info("  ⚠️ ATENÇÃO: Modificações manuais adicionais necessárias!")
            
            self.success_steps.append("Bot modificado (parcial)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao modificar bot: {e}")
            self.failed_steps.append(f"Modificação do bot: {e}")
            return False
    
    def create_test_script(self):
        """Cria script de teste para validar migração"""
        try:
            logger.info("🧪 Criando script de teste...")
            
            test_content = '''#!/usr/bin/env python3
"""Script de teste pós-migração"""

import sys
from pathlib import Path

def test_imports():
    """Testa imports necessários"""
    try:
        from binomo_data_provider import BinomoDataProvider
        print("✅ BinomoDataProvider importado")
        return True
    except ImportError as e:
        print(f"❌ Erro de import: {e}")
        return False

def test_config():
    """Testa configuração"""
    try:
        import json
        with open('config_binomo.json', 'r') as f:
            config = json.load(f)
        print("✅ Configuração carregada")
        return True
    except Exception as e:
        print(f"❌ Erro na configuração: {e}")
        return False

def test_api_connection():
    """Testa conexão com API"""
    try:
        import json
        from binomo_data_provider import BinomoDataProvider
        
        with open('config_binomo.json', 'r') as f:
            config = json.load(f)
        
        provider = BinomoDataProvider(config)
        if provider.test_connection():
            print("✅ Conexão com API OK")
            return True
        else:
            print("❌ Falha na conexão com API")
            return False
    except Exception as e:
        print(f"❌ Erro no teste de API: {e}")
        return False

if __name__ == "__main__":
    print("🧪 TESTE PÓS-MIGRAÇÃO")
    print("=" * 30)
    
    tests = [
        ("Imports", test_imports),
        ("Configuração", test_config), 
        ("API Connection", test_api_connection)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\\nTestando {name}...")
        result = test_func()
        results.append((name, result))
    
    print(f"\\n📋 RESULTADOS:")
    print("=" * 20)
    all_passed = True
    for name, passed in results:
        status = "✅ PASSOU" if passed else "❌ FALHOU"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\\n🎉 TODOS OS TESTES PASSARAM!")
        print("Próximos passos:")
        print("1. Revisar bot.binomo_new.py")
        print("2. Fazer ajustes manuais necessários")
        print("3. Testar bot completo")
    else:
        print(f"\\n⚠️ ALGUNS TESTES FALHARAM")
        print("Verifique os erros acima antes de prosseguir")
'''
            
            with open(self.current_dir / "test_migration.py", 'w') as f:
                f.write(test_content)
            
            logger.info("  ✅ test_migration.py criado")
            self.success_steps.append("Script de teste criado")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar teste: {e}")
            self.failed_steps.append(f"Script de teste: {e}")
            return False
    
    def run_migration(self):
        """Executa migração completa"""
        logger.info("🚀 INICIANDO MIGRAÇÃO PARA API BINOMO")
        logger.info("=" * 50)
        
        steps = [
            ("Validação do ambiente", self.validate_environment),
            ("Criação de backup", self.create_backup),
            ("Criação de arquivos Binomo", self.create_binomo_files),
            ("Modificação do bot", self.modify_bot_file),
            ("Criação de testes", self.create_test_script)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"\n📋 Executando: {step_name}")
            if not step_func():
                logger.error(f"❌ Falha em: {step_name}")
                break
        
        # Relatório final
        self.generate_report()
    
    def generate_report(self):
        """Gera relatório final da migração"""
        logger.info("\n" + "=" * 50)
        logger.info("📋 RELATÓRIO DE MIGRAÇÃO")
        logger.info("=" * 50)
        
        logger.info(f"\n✅ PASSOS CONCLUÍDOS ({len(self.success_steps)}):")
        for step in self.success_steps:
            logger.info(f"  ✅ {step}")
        
        if self.failed_steps:
            logger.info(f"\n❌ PASSOS COM FALHA ({len(self.failed_steps)}):")
            for step in self.failed_steps:
                logger.info(f"  ❌ {step}")
        
        if len(self.failed_steps) == 0:
            logger.info(f"\n🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!")
            logger.info(f"\n📋 PRÓXIMOS PASSOS MANUAIS:")
            logger.info("1. Revisar bot.binomo_new.py")
            logger.info("2. Aplicar modificações específicas nos métodos")
            logger.info("3. Executar: python test_migration.py")
            logger.info("4. Testar bot completo")
            logger.info("5. Renomear bot.binomo_new.py para bot.binomo.py")
        else:
            logger.info(f"\n⚠️ MIGRAÇÃO PARCIAL - AÇÃO MANUAL NECESSÁRIA")
            logger.info("Verifique os erros acima e execute correções")
        
        logger.info(f"\n💾 Backup disponível em: {self.backup_dir}")

def main():
    """Função principal"""
    migration = BinomoMigration()
    migration.run_migration()

if __name__ == "__main__":
    main()
