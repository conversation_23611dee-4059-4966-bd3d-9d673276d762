#!/usr/bin/env python3
"""
Test script for Binomo API integration

Tests the BinomoDataProvider and API connectivity
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime, timezone

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_binomo_api():
    """Test Binomo API functionality"""
    print("🔬 TESTING BINOMO API INTEGRATION")
    print("=" * 50)
    
    try:
        # Import the data provider
        from binomo_data_provider import BinomoDataProvider
        print("✅ BinomoDataProvider imported successfully")
        
        # Load configuration
        config_file = Path('config_binomo.json')
        if config_file.exists():
            with open(config_file, 'r') as f:
                config = json.load(f)
            print("✅ Configuration loaded from config_binomo.json")
        else:
            # Fallback configuration
            config = {
                'BINOMO_API_BASE': 'https://api.binomo.com',
                'SYMBOL': 'Z-CRY%2FIDX',
                'TIMEFRAME': '1',
                'LOCALE': 'br',
                'REQUEST_TIMEOUT': 10,
                'MAX_RETRIES': 3,
                'CACHE_DURATION': 60
            }
            print("⚠️ Using fallback configuration")
        
        # Initialize data provider
        print(f"\n🔌 Initializing Binomo data provider...")
        print(f"   API Base: {config['BINOMO_API_BASE']}")
        print(f"   Symbol: {config['SYMBOL']}")
        print(f"   Timeframe: {config['TIMEFRAME']} minutes")
        
        provider = BinomoDataProvider(config)
        print("✅ BinomoDataProvider initialized")
        
        # Test connection
        print(f"\n🧪 Testing API connection...")
        if provider.test_connection():
            print("✅ API connection successful")
        else:
            print("❌ API connection failed")
            return False
        
        # Test data retrieval
        print(f"\n📊 Testing data retrieval...")
        df = provider.get_historical_data(timeframe="1", count=10)
        
        if df is not None and len(df) > 0:
            print(f"✅ Retrieved {len(df)} candles")
            print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            print(f"💰 Latest OHLC: O={df.iloc[-1]['open']:.5f}, H={df.iloc[-1]['high']:.5f}, L={df.iloc[-1]['low']:.5f}, C={df.iloc[-1]['close']:.5f}")
            print(f"📊 Volume: {df.iloc[-1]['volume']}")
            
            # Display sample data
            print(f"\n📋 Sample data (last 3 candles):")
            sample_df = df.tail(3)[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            for idx, row in sample_df.iterrows():
                ts = row['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                print(f"   {ts} | O:{row['open']:.5f} H:{row['high']:.5f} L:{row['low']:.5f} C:{row['close']:.5f} V:{row['volume']}")
            
        else:
            print("❌ Failed to retrieve data")
            return False
        
        # Test different timeframes
        print(f"\n⏱️ Testing different timeframes...")
        timeframes = ["1", "5", "15"]
        for tf in timeframes:
            print(f"   Testing {tf}m timeframe...", end=" ")
            try:
                tf_df = provider.get_historical_data(timeframe=tf, count=5)
                if tf_df is not None and len(tf_df) > 0:
                    print(f"✅ {len(tf_df)} candles")
                else:
                    print("❌ No data")
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Test symbol validation
        print(f"\n🔍 Testing symbol availability...")
        symbols = provider.get_available_symbols()
        print(f"✅ Available symbols: {symbols}")
        
        # Cleanup
        provider.shutdown()
        print(f"\n✅ Test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure binomo_data_provider.py is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_original_api_files():
    """Test the original API files"""
    print(f"\n🧪 TESTING ORIGINAL API FILES")
    print("=" * 50)
    
    try:
        # Test the link generator
        sys.path.append(str(Path('API-BINOMO')))
        from binApiLinkGen import getLinkApi, getTimeDate
        from getCan import getCandlesBrute
        
        print("✅ Original API modules imported")
        
        # Test time/date generation
        time_str, date_str = getTimeDate(1)
        print(f"📅 Generated time: {time_str}, date: {date_str}")
        
        # Test URL generation
        url = getLinkApi(1)
        print(f"🔗 Generated URL: {url}")
        
        # Test API call
        print(f"🌐 Testing API call...")
        data = getCandlesBrute(url)
        
        if data is not None:
            print(f"✅ API call successful - received {len(data) if isinstance(data, list) else 'unknown'} items")
            if isinstance(data, list) and len(data) > 0:
                print(f"📊 Sample data: {data[0] if len(data) > 0 else 'No data'}")
        else:
            print("❌ API call returned None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing original API files: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 BINOMO API INTEGRATION TEST SUITE")
    print("=" * 60)
    
    results = []
    
    # Test original API files
    print("\n1️⃣ Testing original API files...")
    results.append(("Original API Files", test_original_api_files()))
    
    # Test enhanced data provider
    print("\n2️⃣ Testing enhanced BinomoDataProvider...")
    results.append(("BinomoDataProvider", test_binomo_api()))
    
    # Summary
    print(f"\n📋 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print(f"\n🚀 NEXT STEPS:")
        print("1. Update your config.json to use Binomo settings")
        print("2. Run: python price3.py")
        print("3. Monitor logs for any issues")
    else:
        print(f"\n🔧 TROUBLESHOOTING:")
        print("1. Check your internet connection")
        print("2. Verify Binomo API is accessible")
        print("3. Check binomo_data_provider.py is present")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)