#!/usr/bin/env python3
"""
Enhanced Binomo Data Provider for Price3 Trading Bot

Replaces MT5 data source with direct Binomo API integration.
Provides robust data fetching with caching, retry logic, and error handling.
"""

import json
import time
import logging
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import requests
import pandas as pd
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CandleData:
    """Single candle data structure"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float

class BinomoAPIError(Exception):
    """Custom exception for Binomo API errors"""
    pass

class BinomoDataProvider:
    """
    Enhanced Binomo Data Provider with robust error handling and caching
    
    Features:
    - Multiple timeframe support (1m, 5m, 15m, 1h, 1d)
    - Intelligent caching to reduce API calls
    - Retry logic with exponential backoff
    - Rate limiting protection
    - Data validation and cleaning
    - Real-time and historical data
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.base_url = config.get('BINOMO_API_BASE', 'https://api.binomo.com')
        self.symbol = config.get('SYMBOL', 'Z-CRY%2FIDX')
        self.locale = config.get('LOCALE', 'br')
        self.timeout = config.get('REQUEST_TIMEOUT', 10)
        self.max_retries = config.get('MAX_RETRIES', 3)
        self.cache_duration = config.get('CACHE_DURATION', 60)  # seconds
        
        # Initialize cache
        self.cache = {}
        self.cache_dir = Path('data/cache')
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds between requests
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.timeout = self.timeout
        
        logger.info(f"BinomoDataProvider initialized for symbol: {self.symbol}")
    
    def get_historical_data(self, timeframe: str = "1", count: int = 100, 
                          end_time: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Get historical candlestick data from Binomo API
        
        Args:
            timeframe: Timeframe in minutes ("1", "5")
            count: Number of candles to retrieve
            end_time: End time for historical data (UTC)
        
        Returns:
            DataFrame with OHLCV data or None if error
        """
        if end_time is None:
            # Use dynamic timestamps - try different recent hours to get varied data
            now_utc = datetime.now(timezone.utc)

            # Try multiple recent timestamps to get more varied data
            possible_times = []
            for hours_back in range(1, 25):  # Last 24 hours
                test_time = now_utc - timedelta(hours=hours_back)
                # Round to nearest hour for better API compatibility
                test_time = test_time.replace(minute=0, second=0, microsecond=0)
                possible_times.append(test_time)

            # Use a rotating timestamp based on current minute to get variety
            current_minute = now_utc.minute
            selected_index = current_minute % len(possible_times)
            end_time = possible_times[selected_index]
        
        try:
            # Binomo API returns data from the specified time onwards, so we can get all data in one request
            batch_data = self._fetch_candles_batch(timeframe, end_time, count)
            
            if batch_data is None or len(batch_data) == 0:
                logger.warning("No data received from Binomo API")
                return None
            
            # Convert to DataFrame
            df = self._candles_to_dataframe(batch_data)
            
            # Sort by timestamp and take only requested count
            df = df.sort_values('timestamp').tail(count).reset_index(drop=True)
            
            logger.info(f"Retrieved {len(df)} candles for timeframe {timeframe}m")
            return df
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            return None
    
    def _fetch_candles_batch(self, timeframe: str, end_time: datetime, 
                           count: int) -> Optional[List[CandleData]]:
        """Fetch a single batch of candles from API"""
        
        # Create cache key
        cache_key = self._create_cache_key(timeframe, end_time, count)
        
        # Check cache first
        cached_data = self._get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data
        
        # Build API URL
        url = self._build_url(timeframe, end_time)
        
        # Make API request with retry logic
        data = self._make_request_with_retry(url)
        if data is None:
            return None
        
        # Parse and validate data
        candles = self._parse_api_response(data)
        if candles is None:
            return None
        
        # Cache the results
        self._cache_data(cache_key, candles)
        
        return candles
    
    def _build_url(self, timeframe: str, timestamp: datetime) -> str:
        """Build Binomo API URL"""
        # Format timestamp for API (Binomo expects ISO format)
        time_str = timestamp.strftime('%Y-%m-%dT%H:%M:%S')
        
        # URL encode the symbol if needed
        encoded_symbol = self.symbol.replace('/', '%2F')
        
        url = f"{self.base_url}/candles/v1/{encoded_symbol}/{time_str}/{timeframe}"
        
        if self.locale:
            url += f"?locale={self.locale}"
        
        return url
    
    def _make_request_with_retry(self, url: str) -> Optional[Dict]:
        """Make HTTP request with retry logic and exponential backoff"""
        
        for attempt in range(self.max_retries):
            try:
                # Rate limiting
                self._rate_limit()
                
                logger.debug(f"Making API request (attempt {attempt + 1}): {url}")
                print(f"Making request to: {url}")  # Debug print
                
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                # Track request time for rate limiting
                self.last_request_time = time.time()
                
                data = response.json()
                print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")  # Debug print
                print(f"Data count: {len(data.get('data', [])) if isinstance(data, dict) else 'N/A'}")  # Debug print
                
                logger.debug(f"API request successful, received {len(data.get('data', []))} candles")
                
                return data
                
            except requests.exceptions.Timeout:
                logger.warning(f"Request timeout (attempt {attempt + 1})")
            except requests.exceptions.HTTPError as e:
                logger.warning(f"HTTP error {e.response.status_code} (attempt {attempt + 1})")
                if e.response.status_code == 429:  # Rate limited
                    sleep_time = (2 ** attempt) * 2  # Longer backoff for rate limiting
                    logger.info(f"Rate limited, sleeping for {sleep_time}s")
                    time.sleep(sleep_time)
                    continue
            except requests.exceptions.ConnectionError:
                logger.warning(f"Connection error (attempt {attempt + 1})")
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON response (attempt {attempt + 1})")
            except Exception as e:
                logger.warning(f"Unexpected error: {e} (attempt {attempt + 1})")
            
            if attempt < self.max_retries - 1:
                sleep_time = (2 ** attempt) + 1  # Exponential backoff
                logger.info(f"Retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)
        
        logger.error(f"Failed to fetch data after {self.max_retries} attempts")
        return None
    
    def _parse_api_response(self, data: Dict) -> Optional[List[CandleData]]:
        """Parse API response and validate data"""
        try:
            if 'data' not in data:
                logger.error("API response missing 'data' field")
                return None
            
            candles = []
            raw_candles = data['data']
            
            if not raw_candles:
                logger.warning("API returned empty data array")
                return []
            
            for candle in raw_candles:
                try:
                    # Binomo API format: {'open': float, 'high': float, 'low': float, 'close': float, 'created_at': 'ISO_string'}
                    if isinstance(candle, dict):
                        timestamp = self._parse_timestamp(candle.get('created_at'))
                        open_price = float(candle.get('open', 0))
                        high_price = float(candle.get('high', 0))
                        low_price = float(candle.get('low', 0))
                        close_price = float(candle.get('close', 0))
                        volume = 1.0  # Binomo doesn't provide volume, use default
                        
                    else:
                        logger.warning(f"Unknown candle format: {candle}")
                        continue
                    
                    # Validate data
                    if not self._validate_candle_data(open_price, high_price, low_price, close_price):
                        logger.warning(f"Invalid candle data: O={open_price}, H={high_price}, L={low_price}, C={close_price}")
                        continue
                    
                    candle_data = CandleData(
                        timestamp=timestamp,
                        open=open_price,
                        high=high_price,
                        low=low_price,
                        close=close_price,
                        volume=volume
                    )
                    
                    candles.append(candle_data)
                    
                except (ValueError, TypeError, KeyError) as e:
                    logger.warning(f"Error parsing candle: {e}")
                    continue
            
            logger.debug(f"Successfully parsed {len(candles)} candles from {len(raw_candles)} raw candles")
            return candles
            
        except Exception as e:
            logger.error(f"Error parsing API response: {e}")
            return None
    
    def _parse_timestamp(self, timestamp) -> datetime:
        """Parse timestamp from various formats"""
        if isinstance(timestamp, datetime):
            return timestamp.replace(tzinfo=timezone.utc)
        
        if isinstance(timestamp, (int, float)):
            # Unix timestamp (seconds or milliseconds)
            if timestamp > 1e10:  # Likely milliseconds
                timestamp = timestamp / 1000
            return datetime.fromtimestamp(timestamp, tz=timezone.utc)
        
        if isinstance(timestamp, str):
            # Binomo ISO format: '2024-09-08T18:00:05.000000Z'
            try:
                # Remove Z and replace with explicit UTC timezone
                if timestamp.endswith('Z'):
                    timestamp = timestamp[:-1] + '+00:00'
                return datetime.fromisoformat(timestamp)
            except:
                try:
                    # Try parsing without timezone, assume UTC
                    dt = datetime.fromisoformat(timestamp.replace('Z', ''))
                    return dt.replace(tzinfo=timezone.utc)
                except:
                    # Last resort: try strptime
                    dt = datetime.strptime(timestamp.replace('Z', ''), '%Y-%m-%dT%H:%M:%S.%f')
                    return dt.replace(tzinfo=timezone.utc)
        
        raise ValueError(f"Unable to parse timestamp: {timestamp}")
    
    def _validate_candle_data(self, open_price: float, high_price: float, 
                            low_price: float, close_price: float) -> bool:
        """Validate candle data integrity"""
        
        # Check for valid prices
        if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
            return False
        
        # Check high/low relationship
        if high_price < low_price:
            return False
        
        # Check if open/close are within high/low range
        if not (low_price <= open_price <= high_price):
            return False
        
        if not (low_price <= close_price <= high_price):
            return False
        
        # Check for extreme price movements (potential data errors)
        price_range = high_price - low_price
        avg_price = (high_price + low_price) / 2
        
        if price_range / avg_price > 0.1:  # More than 10% range might be suspicious
            logger.warning(f"Large price range detected: {price_range/avg_price:.2%}")
        
        return True
    
    def _candles_to_dataframe(self, candles: List[CandleData]) -> pd.DataFrame:
        """Convert candle data to pandas DataFrame"""
        
        data = {
            'timestamp': [c.timestamp for c in candles],
            'open': [c.open for c in candles],
            'high': [c.high for c in candles],
            'low': [c.low for c in candles],
            'close': [c.close for c in candles],
            'volume': [c.volume for c in candles]
        }
        
        df = pd.DataFrame(data)
        
        # Ensure timestamps are sorted
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # Add additional columns for compatibility with existing code
        df['time'] = df['timestamp'].astype(int) // 10**9  # Unix timestamp in seconds
        df['tick_volume'] = df['volume']  # Alias for MT5 compatibility
        
        return df
    
    def _create_cache_key(self, timeframe: str, timestamp: datetime, count: int) -> str:
        """Create unique cache key for data request"""
        key_data = f"{self.symbol}_{timeframe}_{timestamp.isoformat()}_{count}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _cache_data(self, cache_key: str, data: List[CandleData]):
        """Cache data to memory and disk"""
        # Memory cache
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
        
        # Disk cache
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"
            cache_data = {
                'data': [
                    {
                        'timestamp': c.timestamp.isoformat(),
                        'open': c.open,
                        'high': c.high,
                        'low': c.low,
                        'close': c.close,
                        'volume': c.volume
                    }
                    for c in data
                ],
                'cached_at': time.time()
            }
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)
        except Exception as e:
            logger.warning(f"Failed to cache data to disk: {e}")
    
    def _get_cached_data(self, cache_key: str) -> Optional[List[CandleData]]:
        """Get cached data if still valid"""
        
        # Check memory cache first - use shorter cache for more dynamic data
        if cache_key in self.cache:
            cached = self.cache[cache_key]
            # Reduce cache time to 15 seconds for more dynamic analysis
            dynamic_cache_time = min(self.cache_duration, 15)
            if time.time() - cached['timestamp'] < dynamic_cache_time:
                logger.debug(f"Using cached data from memory")
                return cached['data']
            else:
                # Expired, remove from memory
                del self.cache[cache_key]
        
        # Check disk cache
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"
            if cache_file.exists():
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)
                
                if time.time() - cached_data['cached_at'] < self.cache_duration:
                    # Convert back to CandleData objects
                    candles = [
                        CandleData(
                            timestamp=datetime.fromisoformat(c['timestamp']),
                            open=c['open'],
                            high=c['high'],
                            low=c['low'],
                            close=c['close'],
                            volume=c['volume']
                        )
                        for c in cached_data['data']
                    ]
                    logger.debug(f"Using cached data from disk")
                    return candles
        except Exception as e:
            logger.warning(f"Failed to load cached data from disk: {e}")
        
        return None
    
    def _rate_limit(self):
        """Implement rate limiting between requests"""
        if self.last_request_time > 0:
            time_since_last = time.time() - self.last_request_time
            if time_since_last < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last
                logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
                time.sleep(sleep_time)
    
    def get_real_time_data(self) -> Optional[pd.DataFrame]:
        """Get latest candle data (last 1-2 candles)"""
        return self.get_historical_data(timeframe="1", count=2)
    
    def test_connection(self) -> bool:
        """Test if Binomo API is accessible"""
        try:
            # Use a known working timeframe (5 minutes) and recent timestamp
            test_timeframe = "5"  # Known to work from our tests
            test_data = self.get_historical_data(timeframe=test_timeframe, count=1)
            return test_data is not None and len(test_data) > 0
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def get_current_price(self) -> Optional[float]:
        """Get current price of the asset"""
        try:
            # Get the most recent candle data
            df = self.get_historical_data(timeframe="1", count=1)
            if df is not None and len(df) > 0:
                # Return the close price of the most recent candle
                return float(df.iloc[-1]['close'])
            else:
                logger.warning("No data available for current price")
                return None
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            return None

    def get_available_symbols(self) -> List[str]:
        """Return list of available symbols (placeholder for API discovery)"""
        # This would require a different API endpoint to get available symbols
        # For now, return the configured symbol
        return [self.symbol]
    
    def shutdown(self):
        """Cleanup resources"""
        self.session.close()
        logger.info("BinomoDataProvider shut down")