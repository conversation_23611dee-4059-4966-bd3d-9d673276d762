#!/usr/bin/env python3
"""
Teste do Sistema de Expiração Dinâmica
Demonstra como o sistema funciona com diferentes cenários
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

# Simular a classe do bot para teste
class DynamicExpirationTester:
    def __init__(self):
        # Carregar configurações
        with open('config_binomo.json', 'r') as f:
            self.config = json.load(f)
    
    def get_current_trading_session(self):
        """Identifica a sessão de trading atual"""
        current_hour = datetime.now().hour
        
        sessions = self.config.get('TRADING_SESSIONS', {})
        
        if 8 <= current_hour <= 17:
            return sessions.get('EUROPEAN', {'preferred_expiration': 2})
        elif 14 <= current_hour <= 22:
            return sessions.get('AMERICAN', {'preferred_expiration': 2})
        else:
            return sessions.get('ASIAN', {'preferred_expiration': 3})

    def validate_signal_consensus(self, signals):
        """Valida consenso entre indicadores"""
        bullish_signals = 0
        bearish_signals = 0
        neutral_signals = 0
        
        for _, signal in signals.items():
            signal_str = str(signal).lower()
            if any(word in signal_str for word in ['bullish', 'oversold', 'recovery', 'buying']):
                bullish_signals += 1
            elif any(word in signal_str for word in ['bearish', 'overbought', 'selling']):
                bearish_signals += 1
            else:
                neutral_signals += 1
        
        directional_signals = bullish_signals + bearish_signals
        if directional_signals == 0:
            return False, 0
            
        consensus_ratio = max(bullish_signals, bearish_signals) / directional_signals
        return consensus_ratio >= 0.6, consensus_ratio

    def determine_optimal_expiration(self, volatility_factor, confidence_level, signals, rsi_value=50, momentum_strength=0):
        """Determina expiração ótima baseada em múltiplos fatores"""
        exp_config = self.config.get('EXPIRATION_OPTIONS', {})
        min_exp = exp_config.get('MIN_EXPIRATION', 1)
        max_exp = exp_config.get('MAX_EXPIRATION', 5)
        volatility_threshold = exp_config.get('VOLATILITY_THRESHOLD', 1.3)
        
        # Análise de extremos RSI
        rsi_extreme = rsi_value < 25 or rsi_value > 75
        
        # Análise de consenso
        has_consensus, consensus_ratio = self.validate_signal_consensus(signals)
        
        # Análise de sessão
        current_session = self.get_current_trading_session()
        session_preferred = current_session.get('preferred_expiration', 3)
        
        # Lógica de decisão
        if (confidence_level == "MUITO_ALTA" and 
            volatility_factor > volatility_threshold and 
            has_consensus and consensus_ratio > 0.8):
            return max(min_exp, 1)
        
        if (confidence_level == "MUITO_ALTA" and rsi_extreme and 
            momentum_strength > 0.001):
            return max(min_exp, 1 if volatility_factor > 1.2 else 2)
        
        if (confidence_level in ["MUITO_ALTA", "ALTA"] and 
            session_preferred <= 2 and has_consensus):
            return max(min_exp, 2)
        
        if confidence_level in ["ALTA", "MEDIA"]:
            if volatility_factor > 1.1 and has_consensus:
                return max(min_exp, 2)
            else:
                return max(min_exp, min(3, session_preferred))
        
        if confidence_level == "BAIXA":
            return min(max_exp, 5)
        
        return max(min_exp, min(max_exp, session_preferred))

def test_scenarios():
    """Testa diferentes cenários do sistema dinâmico"""
    tester = DynamicExpirationTester()
    
    print("🔄 TESTE DO SISTEMA DE EXPIRAÇÃO DINÂMICA\n")
    print("=" * 60)
    
    # Cenários de teste
    scenarios = [
        {
            "name": "Sinal Muito Forte - Alta Volatilidade",
            "volatility": 1.6,
            "confidence": "MUITO_ALTA",
            "rsi": 22,
            "momentum": 0.005,
            "signals": {
                "rsi": "Oversold Extremo",
                "macd": "Bullish Momentum",
                "bollinger": "Oversold",
                "volume": "Buying Surge"
            }
        },
        {
            "name": "Sinal Forte - RSI Extremo",
            "volatility": 1.2,
            "confidence": "MUITO_ALTA",
            "rsi": 78,
            "momentum": 0.003,
            "signals": {
                "rsi": "Overbought Extremo",
                "macd": "Bearish Signal",
                "trend": "Strong Down"
            }
        },
        {
            "name": "Sinal Médio - Condições Normais",
            "volatility": 1.1,
            "confidence": "MEDIA",
            "rsi": 45,
            "momentum": 0.001,
            "signals": {
                "rsi": "Neutro",
                "macd": "Bullish",
                "bollinger": "Meio"
            }
        },
        {
            "name": "Sinal Fraco - Baixa Volatilidade",
            "volatility": 0.8,
            "confidence": "BAIXA",
            "rsi": 50,
            "momentum": 0.0005,
            "signals": {
                "rsi": "Neutro",
                "macd": "Neutro",
                "trend": "Weak"
            }
        },
        {
            "name": "Sinal Alto - Horário Ativo",
            "volatility": 1.3,
            "confidence": "ALTA",
            "rsi": 35,
            "momentum": 0.002,
            "signals": {
                "rsi": "Recovery",
                "macd": "Bullish",
                "volume": "High",
                "trend": "Up"
            }
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📊 CENÁRIO {i}: {scenario['name']}")
        print("-" * 50)
        
        # Calcular expiração
        expiration = tester.determine_optimal_expiration(
            scenario['volatility'],
            scenario['confidence'],
            scenario['signals'],
            scenario['rsi'],
            scenario['momentum']
        )
        
        # Validar consenso
        has_consensus, consensus_ratio = tester.validate_signal_consensus(scenario['signals'])
        
        # Obter sessão atual
        session = tester.get_current_trading_session()
        current_hour = datetime.now().hour
        session_name = "Europeia" if 8 <= current_hour <= 17 else "Americana" if 14 <= current_hour <= 22 else "Asiática"
        
        print(f"Volatilidade: {scenario['volatility']:.1f}")
        print(f"Confiança: {scenario['confidence']}")
        print(f"RSI: {scenario['rsi']}")
        print(f"Momentum: {scenario['momentum']:.4f}")
        print(f"Consenso: {'✅' if has_consensus else '❌'} ({consensus_ratio:.1%})")
        print(f"Sessão: {session_name} (pref: {session.get('preferred_expiration')}min)")
        print(f"🎯 EXPIRAÇÃO CALCULADA: {expiration} MINUTOS")
        
        # Explicar a lógica
        if expiration == 1:
            print("💡 Razão: Sinal muito forte + alta volatilidade")
        elif expiration == 2:
            print("💡 Razão: Sinal forte + condições favoráveis")
        elif expiration == 3:
            print("💡 Razão: Condições médias ou padrão da sessão")
        elif expiration == 5:
            print("💡 Razão: Sinal fraco, precisa mais tempo")
    
    print("\n" + "=" * 60)
    print("✅ TESTE CONCLUÍDO - Sistema funcionando corretamente!")
    print(f"⏰ Horário atual: {datetime.now().strftime('%H:%M:%S')}")
    print(f"🌍 Sessão ativa: {session_name}")

if __name__ == "__main__":
    test_scenarios()
