#!/usr/bin/env python3
"""
Bot Binomo - Versão com Dados em Tempo Real
Usa BinomoRealtimeProvider para dados dinâmicos e análise técnica variável
"""

import telebot
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from binomo_realtime_provider import BinomoRealtimeProvider
import json
from typing import Optional

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    operation: str
    probability: float
    expiration_time: datetime = None
    exit_price: float = None
    result: str = None

class TradingBotRealtime:
    def __init__(self):
        print("Inicializando TradingBot com dados em tempo real...")
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False
        
        # Inicializar BinomoRealtimeProvider
        self.data_provider = BinomoRealtimeProvider(self.config)
        print("TradingBot inicializado com sucesso!")

    def _load_config(self):
        """Carrega variáveis do .env e config_binomo.json"""
        load_dotenv()
        
        # Configurações básicas do .env
        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0'))
        }
        
        # Carregar configurações da API Binomo
        try:
            with open('config_binomo.json', 'r') as f:
                binomo_config = json.load(f)
                self.config.update(binomo_config)
        except FileNotFoundError:
            # Configurações padrão
            self.config.update({
                'SYMBOL': 'Z-CRY/IDX',
                'TIMEFRAME': '1',
                'USE_REALTIME_API': True
            })

    def prepare_features(self, df):
        """Prepara features para análise técnica com dados em tempo real"""
        try:
            if len(df) < 10:
                logging.warning("Dados insuficientes para análise técnica")
                return None

            # Dados já são dinâmicos do BinomoRealtimeProvider
            df = df.copy()
            
            # Calcular indicadores técnicos
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['roc'] = df['close'].pct_change(periods=3) * 100
            
            # Volume e volatilidade
            df['volume_ratio'] = df['tick_volume'] / df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volatility'] = df['close'].rolling(window=5, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=10, min_periods=1).mean()
            
            # Agressão (body size)
            df['body_size'] = abs(df['close'] - df['open']) / df['open']
            df['aggression_score'] = df['body_size'] * df['volume_ratio'].fillna(1)
            
            # Tendência
            df['trend'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            df['trend_strength'] = abs(df['sma_5'] - df['sma_10']) / df['sma_10'] * 100
            
            # Pressão de compra/venda
            df['buying_pressure'] = np.where(df['close'] > df['open'], df['tick_volume'], 0)
            df['selling_pressure'] = np.where(df['close'] < df['open'], df['tick_volume'], 0)
            df['pressure'] = (df['buying_pressure'] - df['selling_pressure']).rolling(window=5).sum()
            
            return df.fillna(0)
            
        except Exception as e:
            logging.error(f"Erro ao preparar features: {e}")
            return None

    def predict_next_move(self):
        """Análise técnica com dados em tempo real"""
        try:
            # Obter dados em tempo real
            df = self.data_provider.get_historical_data(timeframe="1", count=20)
            
            if df is None or len(df) == 0:
                logging.error("Erro ao obter dados em tempo real")
                return None, 0.0

            features = self.prepare_features(df)
            if features is None:
                return None, 0.0

            last_row = features.iloc[-1]
            
            # Sistema de pontuação dinâmico
            score = 50  # Base neutra
            signals = []

            # Análise de Tendência (30% do peso)
            if last_row['trend'] > 0:
                trend_score = min(15, last_row['trend_strength'] * 2)
                score += trend_score
                signals.append(('Tendência de alta', last_row['trend_strength']))
            else:
                trend_score = min(15, last_row['trend_strength'] * 2)
                score -= trend_score
                signals.append(('Tendência de baixa', last_row['trend_strength']))

            # Análise de Volume (25% do peso)
            if last_row['volume_ratio'] > 1.2:
                score += 12
                signals.append(('Volume forte', last_row['volume_ratio']))
            elif last_row['volume_ratio'] < 0.8:
                score -= 8
                signals.append(('Volume fraco', last_row['volume_ratio']))

            # Análise de Pressão (25% do peso)
            if last_row['pressure'] > 0:
                pressure_score = min(12, abs(last_row['pressure']) / 1000)
                score += pressure_score
                signals.append(('Pressão compradora', last_row['pressure']))
            else:
                pressure_score = min(12, abs(last_row['pressure']) / 1000)
                score -= pressure_score
                signals.append(('Pressão vendedora', last_row['pressure']))

            # Análise de Agressão (20% do peso)
            if last_row['aggression_score'] > 0.01:
                score += min(10, last_row['aggression_score'] * 1000)
                signals.append(('Alta agressão', last_row['aggression_score']))

            # Determinar direção
            direction = '🟩COMPRA' if score > 52 else '🟥VENDA' if score < 48 else None
            
            if direction is None:
                logging.info(f"Sinal neutro - Score: {score:.2f}")
                return None, 0.0

            logging.info(f"Score final: {score:.2f}, Sinais: {signals}")
            return direction, score

        except Exception as e:
            logging.error(f"Erro na análise técnica: {e}")
            return None, 0.0

    def gerar_sinal(self):
        """Gera sinal com controle de estado e dados em tempo real"""
        # Verifica se há trade ativo aguardando resultado
        if self.active_trade and datetime.now() < self.active_trade.expiration_time:
            remaining_time = (self.active_trade.expiration_time - datetime.now()).total_seconds()
            return f"⏳ Trade ativo! Aguarde {remaining_time/60:.1f} minutos para o resultado."
        
        # Se há trade expirado, calcula resultado primeiro
        if self.active_trade and datetime.now() >= self.active_trade.expiration_time:
            self._calculate_and_send_result()
            self.active_trade = None

        if self.processing_signal:
            return "⚠️ Aguarde o fim do sinal atual."

        self.processing_signal = True
        try:
            # Análise técnica com dados em tempo real
            operation, probability = self.predict_next_move()
            if operation is None:
                self.processing_signal = False
                return "⚠️ Nenhum sinal gerado."

            # Obter preço de entrada em tempo real
            entry_price = self.data_provider.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(minutes=3)
            self.active_trade = TradeResult(entry_time, entry_price, operation, probability, expiration_time=expiration_time)

            # Obter dados para análise detalhada
            df = self.data_provider.get_historical_data(count=5)
            if df is not None and len(df) > 0:
                features = self.prepare_features(df)
                if features is not None:
                    last_row = features.iloc[-1]
                    
                    # Enviar sinal detalhado
                    message = (
                        f"🎯 **SINAL DE {operation}**\n"
                        f"📈 Ativo: {self.config.get('SYMBOL', 'CRY/IDX')}\n"
                        f"⏳ Expiração: {expiration_time.strftime('%H:%M:%S')} (M3)\n\n"
                        f"📊 Confiança: {probability:.2f}%\n"
                        f"💰 Entrada: {entry_price:.5f}\n\n"
                        f"📈 **Análise Técnica (TEMPO REAL)**\n"
                        f"Volume: {'🟢' if last_row['volume_ratio'] > 1 else '🔴'} ({last_row['volume_ratio']:.2f}x média)\n"
                        f"Agressão: {'🟢' if last_row['aggression_score'] > 0.01 else '🔴'} ({last_row['aggression_score']:.3f})\n"
                        f"Tendência: {'🟢' if last_row['trend'] > 0 else '🔴'} (Força: {last_row['trend_strength']:.2f}%)\n"
                        f"Volatilidade: {'🟢' if last_row['volatility_ratio'] > 1 else '🔴'} ({last_row['volatility_ratio']:.2f}x média)\n"
                        f"Pressão: {'🟢' if last_row['pressure'] > 0 else '🔴'} ({last_row['pressure']:.0f})"
                    )

                    self.bot.send_message(self.config['chat_id'], message)
                    logging.info(f"Sinal enviado: {operation} - Entrada: {entry_price:.5f}")

            self.processing_signal = False
            return f"✅ Sinal {operation} enviado! Resultado em 3 minutos."

        except Exception as e:
            logging.error(f"Erro ao gerar sinal: {str(e)}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."
    
    def _calculate_and_send_result(self):
        """Calcula e envia resultado do trade"""
        if not self.active_trade:
            return
        
        try:
            exit_price = self.data_provider.get_current_price()
            if exit_price is None:
                logging.error("Erro ao obter preço de fechamento")
                return
            
            # Calcular resultado
            if self.active_trade.operation == '🟩COMPRA':
                is_win = exit_price > self.active_trade.entry_price
            else:
                is_win = exit_price < self.active_trade.entry_price
            
            result = "✅GAIN 📈" if is_win else "❌LOSS 📉"
            price_change = abs(exit_price - self.active_trade.entry_price) / self.active_trade.entry_price * 100
            
            result_message = (
                f"🔄 **RESULTADO DO TRADE (TEMPO REAL)**\n"
                f"Operação: {self.active_trade.operation}\n"
                f"Entrada: {self.active_trade.entry_price:.5f}\n"
                f"Saída: {exit_price:.5f}\n"
                f"Variação: {price_change:.3f}%\n"
                f"Resultado: {result}"
            )
            
            self.bot.send_message(self.config['chat_id'], result_message)
            logging.info(f"Resultado: {result} - Variação: {price_change:.3f}%")
            
        except Exception as e:
            logging.error(f"Erro ao calcular resultado: {e}")

    def _setup_telegram_handlers(self):
        """Configura comandos do Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            self.bot.send_message(message.chat.id, "🤖 Bot de Trading com Dados em Tempo Real! Use /sinal para obter um sinal.")

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal()
            if isinstance(sinal, str):
                self.bot.send_message(message.chat.id, sinal)

    def run(self):
        """Executa o bot"""
        if not self.data_provider.test_connection():
            logging.error("Erro ao conectar com dados em tempo real")
            return
        logging.info("🚀 Bot de trading iniciado com DADOS EM TEMPO REAL!")
        self.bot.polling()

if __name__ == "__main__":
    print("Iniciando bot com dados em tempo real...")
    bot = TradingBotRealtime()
    bot.run()
