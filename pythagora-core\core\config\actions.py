BH_START_BUG_HUNT = "Start bug hunt for task #{}"
BH_WAIT_BUG_REP_INSTRUCTIONS = "Awaiting bug reproduction instructions for task #{}"
BH_START_USER_TEST = "Start user testing for task #{}"
BH_STARTING_PAIR_PROGRAMMING = "Start pair programming for task #{}"

CM_UPDATE_FILES = "Updating files"


DEV_WAIT_TEST = "Awaiting user test"
DEV_TASK_START = "Task #{} start"
DEV_TASK_BREAKDOWN = "Task #{} breakdown"
DEV_TROUBLESHOOT = "Troubleshooting #{}"
DEV_TASK_REVIEW_FEEDBACK = "Task review feedback"

TC_TASK_DONE = "Task #{} complete"


FE_INIT = "Frontend init"
FE_START = "Frontend start"
FE_CONTINUE = "Frontend continue"
FE_ITERATION = "Frontend iteration"
FE_ITERATION_DONE = "Frontend iteration done"

TL_CREATE_INITIAL_EPIC = "Create initial project epic"
TL_CREATE_PLAN = "Create a development plan for epic: {}"
TL_START_FEATURE = "Start of feature #{}"
TL_INITIAL_PROJECT_NAME = "Initial Project"

TW_WRITE = "Write documentation"

EX_SKIP_COMMAND = 'Skip "{}"'
EX_RUN_COMMAND = 'Run "{}"'

SPEC_CREATE_STEP_NAME = "Create specification"
SPEC_CHANGE_STEP_NAME = "Change specification"
SPEC_CHANGE_FEATURE_STEP_NAME = "Change specification due to new feature"

TS_TASK_REVIEWED = "Task #{} reviewed"
TS_ALT_SOLUTION = "Alternative solution (attempt #{})"
TS_APP_WORKING = "Please check if the app is working"

PS_EPIC_COMPLETE = "Epic {} completed"

# other constants
TL_EDIT_DEV_PLAN = "Open and edit your development plan in the Progress tab"
MIX_BREAKDOWN_CHAT_PROMPT = "Are you happy with the breakdown? Now is a good time to ask questions or suggest changes."
FE_CHANGE_REQ = (
    "Do you want to change anything or report a bug? Keep in mind that currently ONLY frontend is implemented."
)
FE_DONE_WITH_UI = "Are you sure you're done building the UI and want to start building the backend functionality now?"
TS_DESCRIBE_ISSUE = "Please describe the issue you found (one at a time) and share any relevant server logs"
BH_HUMAN_TEST_AGAIN = "Please test the app again."
BH_IS_BUG_FIXED = "Is the bug you reported fixed now?"
BH_ADDITIONAL_FEEDBACK = "Please add any additional feedback that could help Pythagora solve this bug"
HUMAN_INTERVENTION_QUESTION = "I need human intervention"
CONTINUE_WHEN_DONE = 'When you\'re done, just click "Continue"'
RUN_COMMAND = "Can I run command:"
DEV_EXECUTE_TASK = "Do you want to execute the above task?"
